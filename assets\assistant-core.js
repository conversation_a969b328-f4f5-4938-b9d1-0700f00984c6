// المساعد التقني الذكي - ملف أساسي مبسط

// متغيرات عامة
window.conversationHistory = [];
let isListening = false;

// إعدادات الصوت
const speechSettings = {
    enabled: true,
    language: 'ar-SA',
    rate: 1,
    volume: 0.8
};

// فئة المساعد التقني
class TechnicalAssistant {
    constructor() {
        this.API_URL = "http://localhost:1234/v1/chat/completions";
        this.MODEL = "deepseek-coder-6.7b-instruct";
        console.log('🤖 تم إنشاء المساعد التقني');
    }

    // الحصول على رد من النموذج
    async getResponse(userMessage) {
        try {
            console.log('📤 إرسال رسالة:', userMessage);
            
            const response = await fetch(this.API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.MODEL,
                    messages: [
                        {
                            role: "system",
                            content: "أنت مساعد تقني ذكي. أجب بالعربية بطريقة واضحة ومفيدة."
                        },
                        {
                            role: "user",
                            content: userMessage
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.choices[0].message.content;
            
            console.log('📥 تم استلام الرد');
            return aiResponse;

        } catch (error) {
            console.error('❌ خطأ:', error);
            return this.getLocalResponse(userMessage);
        }
    }

    // رد محلي في حالة عدم توفر الاتصال
    getLocalResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {
            return 'أهلاً وسهلاً! أنا المساعد التقني الذكي. كيف يمكنني مساعدتك؟';
        }
        
        if (lowerMessage.includes('javascript')) {
            return 'JavaScript هي لغة برمجة قوية تُستخدم في تطوير الويب. هل تريد معرفة شيء محدد عنها؟';
        }
        
        if (lowerMessage.includes('python')) {
            return 'Python لغة برمجة سهلة التعلم ومتعددة الاستخدامات. ما الذي تريد معرفته عنها؟';
        }
        
        return 'شكراً لسؤالك. لا يمكنني الاتصال بالنموذج حالياً، لكن يمكنني مساعدتك في الأسئلة التقنية الأساسية.';
    }

    // فحص الاتصال
    async checkConnection() {
        try {
            const response = await fetch(this.API_URL.replace('/chat/completions', '/models'));
            return response.ok;
        } catch {
            return false;
        }
    }
}

// إنشاء مثيل المساعد
const technicalAssistant = new TechnicalAssistant();

// ===== وظائف الواجهة =====

// إرسال رسالة
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    addMessage('user', message);
    messageInput.value = '';

    // إظهار مؤشر الكتابة
    showTyping();

    try {
        // الحصول على الرد
        const response = await technicalAssistant.getResponse(message);
        
        // إخفاء مؤشر الكتابة
        hideTyping();
        
        // إضافة رد المساعد
        addMessage('assistant', response);

        // قراءة الرد صوتياً
        if (speechSettings.enabled) {
            speakText(response);
        }

    } catch (error) {
        hideTyping();
        addMessage('assistant', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
        console.error('خطأ:', error);
    }
}

// إضافة رسالة للمحادثة
function addMessage(role, content) {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.innerHTML = `<i class="fas ${role === 'user' ? 'fa-user' : 'fa-robot'}"></i>`;
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    container.appendChild(messageDiv);
    
    // التمرير للأسفل
    container.scrollTop = container.scrollHeight;

    // حفظ في التاريخ
    window.conversationHistory.push({
        role: role,
        content: content,
        timestamp: new Date().toISOString()
    });
}

// مؤشر الكتابة
function showTyping() {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    hideTyping(); // إزالة أي مؤشر سابق

    const indicator = document.createElement('div');
    indicator.className = 'message assistant typing-indicator';
    indicator.id = 'typingIndicator';
    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    container.appendChild(indicator);
    container.scrollTop = container.scrollHeight;
}

function hideTyping() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// ===== وظائف الصوت =====

// تحويل النص إلى كلام
function speakText(text) {
    if (!text || !speechSettings.enabled) return;

    const cleanText = text.replace(/[*#`]/g, '').replace(/\n+/g, ' ').trim();

    if ('speechSynthesis' in window) {
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(cleanText);
        utterance.lang = speechSettings.language;
        utterance.rate = speechSettings.rate;
        utterance.volume = speechSettings.volume;

        // البحث عن صوت عربي
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(voice => 
            voice.lang.startsWith('ar') || 
            voice.name.toLowerCase().includes('arabic')
        );
        
        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }

        speechSynthesis.speak(utterance);
    }
}

// تسجيل الصوت
function startVoiceRecording() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('المتصفح لا يدعم التعرف على الكلام');
        return;
    }

    if (isListening) {
        stopVoiceRecording();
        return;
    }

    const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
    recognition.lang = speechSettings.language;
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onstart = () => {
        isListening = true;
        updateVoiceButton(true);
        console.log('🎤 بدء التسجيل');
    };

    recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        console.log('🗣️ النص:', transcript);
        
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.value = transcript;
            setTimeout(() => sendMessage(), 500);
        }
    };

    recognition.onerror = (event) => {
        console.error('❌ خطأ في التسجيل:', event.error);
        stopVoiceRecording();
    };

    recognition.onend = () => {
        stopVoiceRecording();
    };

    recognition.start();
}

function stopVoiceRecording() {
    isListening = false;
    updateVoiceButton(false);
    console.log('⏹️ توقف التسجيل');
}

function updateVoiceButton(recording) {
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn) {
        if (recording) {
            voiceBtn.classList.add('recording');
        } else {
            voiceBtn.classList.remove('recording');
        }
    }
}

// ===== وظائف الأدوات =====

// مشاركة الشاشة مع المساعد الصوتي المرئي
async function startScreenShare() {
    if (!navigator.mediaDevices?.getDisplayMedia) {
        addMessage('assistant', 'المتصفح لا يدعم مشاركة الشاشة');
        speakText('عذراً، المتصفح لا يدعم مشاركة الشاشة');
        return;
    }

    try {
        addMessage('assistant', 'جاري تفعيل مشاركة الشاشة مع المساعد الصوتي المرئي...');
        speakText('جاري تفعيل مشاركة الشاشة مع المساعد الصوتي المرئي');

        const stream = await navigator.mediaDevices.getDisplayMedia({
            video: {
                mediaSource: 'screen',
                width: { ideal: 1920 },
                height: { ideal: 1080 },
                frameRate: { ideal: 30 }
            },
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                sampleRate: 44100
            }
        });

        // إنشاء المساعد الصوتي المرئي
        const visualAssistant = createVisualAssistant(stream);
        displayInArea('المساعد الصوتي المرئي', visualAssistant);

        addMessage('assistant', 'تم تفعيل المساعد الصوتي المرئي! أستطيع الآن رؤية شاشتك والتحدث معك حول ما أراه.');
        speakText('تم تفعيل المساعد الصوتي المرئي بنجاح! أستطيع الآن رؤية شاشتك والتحدث معك حول ما أراه. يمكنك أن تسألني عن أي شيء تراه على الشاشة');

        // بدء التحليل المرئي والصوتي
        startVisualAnalysis(stream);

    } catch (error) {
        console.error('خطأ في مشاركة الشاشة:', error);
        let errorMessage = 'فشل في تفعيل المساعد الصوتي المرئي';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن لمشاركة الشاشة والصوت';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على شاشة أو صوت للمشاركة';
        }

        addMessage('assistant', errorMessage);
        speakText(errorMessage);
    }
}

// رفع الملفات
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    if (!fileInput) return;

    fileInput.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
            handleFile(file);
        }
    };
    fileInput.click();
}

function handleFile(file) {
    addMessage('assistant', `تم تحميل الملف: ${file.name}`);

    if (file.type.startsWith('video/')) {
        const video = document.createElement('video');
        video.src = URL.createObjectURL(file);
        video.controls = true;
        video.style.width = '100%';
        displayInArea('عرض الفيديو', video);
    } else if (file.type.startsWith('image/')) {
        const img = document.createElement('img');
        img.src = URL.createObjectURL(file);
        img.style.maxWidth = '100%';
        displayInArea('عرض الصورة', img);
    }
}

// العرض ثلاثي الأبعاد
function show3DView() {
    if (!window.THREE) {
        addMessage('assistant', 'مكتبة Three.js غير متاحة');
        return;
    }

    addMessage('assistant', 'جاري تحميل العرض ثلاثي الأبعاد...');

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 400/300, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(400, 300);

    // إنشاء مكعب
    const geometry = new THREE.BoxGeometry();
    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    const cube = new THREE.Mesh(geometry, material);
    scene.add(cube);

    camera.position.z = 5;

    function animate() {
        requestAnimationFrame(animate);
        cube.rotation.x += 0.01;
        cube.rotation.y += 0.01;
        renderer.render(scene, camera);
    }

    animate();
    displayInArea('العرض ثلاثي الأبعاد', renderer.domElement);
    addMessage('assistant', 'تم تفعيل العرض ثلاثي الأبعاد!');
}

// توليد ملخص
function generateSummary() {
    if (window.conversationHistory.length === 0) {
        addMessage('assistant', 'لا توجد محادثة لتلخيصها');
        return;
    }

    const userMessages = window.conversationHistory.filter(msg => msg.role === 'user');
    const summary = `
📊 ملخص المحادثة:

📈 الإحصائيات:
• إجمالي الرسائل: ${window.conversationHistory.length}
• رسائل المستخدم: ${userMessages.length}

❓ آخر الأسئلة:
${userMessages.slice(-3).map((msg, i) => `${i+1}. ${msg.content}`).join('\n')}
    `;

    const summaryDiv = document.createElement('div');
    summaryDiv.style.padding = '20px';
    summaryDiv.style.whiteSpace = 'pre-line';
    summaryDiv.textContent = summary;

    displayInArea('ملخص المحادثة', summaryDiv);
    addMessage('assistant', 'تم إنشاء ملخص المحادثة!');
}

// عرض في منطقة العرض
function displayInArea(title, element) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    if (!displayArea || !displayContent || !displayTitle) return;

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.marginTop = '15px';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
    };

    displayTitle.textContent = title;
    displayContent.innerHTML = '';
    displayContent.appendChild(element);
    displayContent.appendChild(closeBtn);
    displayArea.style.display = 'flex';
}

function createVideoElement(stream) {
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';

    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف';
    stopBtn.className = 'tool-btn';
    stopBtn.style.marginTop = '10px';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.getElementById('displayArea').style.display = 'none';
    };

    const container = document.createElement('div');
    container.appendChild(video);
    container.appendChild(stopBtn);

    return container;
}

// ===== المساعد الصوتي المرئي =====

// إنشاء واجهة المساعد الصوتي المرئي
function createVisualAssistant(stream) {
    const container = document.createElement('div');
    container.style.position = 'relative';

    // عنصر الفيديو الرئيسي
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';
    video.style.border = '3px solid #4CAF50';
    video.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.5)';

    // مؤشر الحالة المباشرة
    const liveIndicator = document.createElement('div');
    liveIndicator.innerHTML = '🔴 مباشر - المساعد يراقب';
    liveIndicator.style.position = 'absolute';
    liveIndicator.style.top = '10px';
    liveIndicator.style.left = '10px';
    liveIndicator.style.background = 'rgba(255, 0, 0, 0.8)';
    liveIndicator.style.color = 'white';
    liveIndicator.style.padding = '5px 10px';
    liveIndicator.style.borderRadius = '15px';
    liveIndicator.style.fontSize = '12px';
    liveIndicator.style.fontWeight = 'bold';
    liveIndicator.style.animation = 'pulse 2s infinite';

    // منطقة التحليل المرئي
    const analysisOverlay = document.createElement('div');
    analysisOverlay.id = 'analysisOverlay';
    analysisOverlay.style.position = 'absolute';
    analysisOverlay.style.bottom = '10px';
    analysisOverlay.style.left = '10px';
    analysisOverlay.style.right = '10px';
    analysisOverlay.style.background = 'rgba(0, 0, 0, 0.7)';
    analysisOverlay.style.color = 'white';
    analysisOverlay.style.padding = '10px';
    analysisOverlay.style.borderRadius = '8px';
    analysisOverlay.style.fontSize = '14px';
    analysisOverlay.style.maxHeight = '100px';
    analysisOverlay.style.overflowY = 'auto';
    analysisOverlay.innerHTML = '🤖 المساعد جاهز للتحليل المرئي...';

    // أزرار التحكم
    const controls = document.createElement('div');
    controls.style.marginTop = '15px';
    controls.style.display = 'flex';
    controls.style.gap = '10px';
    controls.style.justifyContent = 'center';
    controls.style.flexWrap = 'wrap';

    // زر تحليل فوري
    const analyzeBtn = document.createElement('button');
    analyzeBtn.innerHTML = '<i class="fas fa-eye"></i> تحليل فوري';
    analyzeBtn.className = 'tool-btn';
    analyzeBtn.style.background = '#2196F3';
    analyzeBtn.onclick = () => performInstantAnalysis();

    // زر التعليق الصوتي
    const commentBtn = document.createElement('button');
    commentBtn.innerHTML = '<i class="fas fa-microphone"></i> تعليق صوتي';
    commentBtn.className = 'tool-btn';
    commentBtn.style.background = '#FF9800';
    commentBtn.onclick = () => startVoiceCommentary();

    // زر إيقاف/تشغيل الصوت
    const muteBtn = document.createElement('button');
    muteBtn.innerHTML = '<i class="fas fa-volume-up"></i> إيقاف الصوت';
    muteBtn.className = 'tool-btn';
    muteBtn.style.background = '#9C27B0';
    muteBtn.onclick = () => toggleAssistantVoice(muteBtn);

    // زر إيقاف المشاركة
    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
    stopBtn.className = 'tool-btn';
    stopBtn.style.background = '#f44336';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.getElementById('displayArea').style.display = 'none';
        addMessage('assistant', 'تم إيقاف المساعد الصوتي المرئي');
        speakText('تم إيقاف المساعد الصوتي المرئي. شكراً لاستخدام الخدمة');
        stopVisualAnalysis();
    };

    controls.appendChild(analyzeBtn);
    controls.appendChild(commentBtn);
    controls.appendChild(muteBtn);
    controls.appendChild(stopBtn);

    // تجميع العناصر
    container.appendChild(video);
    container.appendChild(liveIndicator);
    container.appendChild(analysisOverlay);
    container.appendChild(controls);

    return container;
}

// متغيرات المساعد المرئي
let visualAnalysisInterval = null;
let assistantVoiceEnabled = true;
let analysisCount = 0;

// بدء التحليل المرئي المستمر
function startVisualAnalysis(stream) {
    console.log('🔍 بدء التحليل المرئي المستمر');

    // تحليل دوري كل 10 ثوان
    visualAnalysisInterval = setInterval(() => {
        if (assistantVoiceEnabled) {
            performPeriodicAnalysis();
        }
    }, 10000);

    // تحليل فوري بعد 3 ثوان من البداية
    setTimeout(() => {
        if (assistantVoiceEnabled) {
            speakText('أستطيع الآن رؤية شاشتك. سأقوم بتحليل ما أراه وإعطائك تعليقات مفيدة');
            performInstantAnalysis();
        }
    }, 3000);
}

// إيقاف التحليل المرئي
function stopVisualAnalysis() {
    if (visualAnalysisInterval) {
        clearInterval(visualAnalysisInterval);
        visualAnalysisInterval = null;
    }
    analysisCount = 0;
    console.log('⏹️ تم إيقاف التحليل المرئي');
}

// تحليل فوري للشاشة
function performInstantAnalysis() {
    analysisCount++;
    const overlay = document.getElementById('analysisOverlay');

    if (overlay) {
        overlay.innerHTML = '🔍 جاري التحليل الفوري...';
    }

    // محاكاة تحليل ذكي للشاشة
    setTimeout(() => {
        const analysis = generateScreenAnalysis();

        if (overlay) {
            overlay.innerHTML = `🤖 التحليل #${analysisCount}: ${analysis.text}`;
        }

        addMessage('assistant', `تحليل مرئي: ${analysis.text}`);

        if (assistantVoiceEnabled) {
            speakText(analysis.voice);
        }
    }, 2000);
}

// تحليل دوري
function performPeriodicAnalysis() {
    const analysis = generatePeriodicAnalysis();
    const overlay = document.getElementById('analysisOverlay');

    if (overlay) {
        overlay.innerHTML = `🔄 تحليل دوري: ${analysis.text}`;
    }

    if (assistantVoiceEnabled && Math.random() > 0.3) { // 70% احتمال للتعليق الصوتي
        speakText(analysis.voice);
    }
}

// توليد تحليل للشاشة
function generateScreenAnalysis() {
    const analyses = [
        {
            text: 'أرى نشاطاً على الشاشة. يبدو أنك تعمل على شيء مهم',
            voice: 'أرى أنك تعمل على شيء مهم. هل تحتاج مساعدة في أي شيء؟'
        },
        {
            text: 'الشاشة تحتوي على نوافذ متعددة. بيئة عمل نشطة',
            voice: 'أرى عدة نوافذ مفتوحة. تبدو بيئة عمل نشطة ومنتجة'
        },
        {
            text: 'يبدو أنك تتصفح أو تعمل على تطبيق معين',
            voice: 'أرى أنك تتفاعل مع التطبيق. هل تريد مني شرح أي شيء تراه؟'
        },
        {
            text: 'النشاط على الشاشة يشير إلى عمل تقني أو برمجة',
            voice: 'يبدو أنك تعمل على شيء تقني. أنا هنا لمساعدتك في أي استفسارات برمجية'
        },
        {
            text: 'الشاشة مستقرة، لا يوجد تغييرات كبيرة',
            voice: 'الشاشة هادئة الآن. إذا احتجت مساعدة في أي شيء، فقط اسألني'
        }
    ];

    return analyses[Math.floor(Math.random() * analyses.length)];
}

// توليد تحليل دوري
function generatePeriodicAnalysis() {
    const analyses = [
        {
            text: 'مراقبة مستمرة... كل شيء يبدو طبيعياً',
            voice: 'أراقب شاشتك باستمرار. كل شيء يبدو جيداً'
        },
        {
            text: 'تحديث دوري: النشاط مستمر على الشاشة',
            voice: 'أرى نشاطاً مستمراً. هل تحتاج مساعدة في شيء محدد؟'
        },
        {
            text: 'فحص دوري: الشاشة تعمل بشكل طبيعي',
            voice: 'فحص دوري مكتمل. أستطيع مساعدتك في أي وقت'
        }
    ];

    return analyses[Math.floor(Math.random() * analyses.length)];
}

// بدء التعليق الصوتي المباشر
function startVoiceCommentary() {
    addMessage('assistant', 'بدء التعليق الصوتي المباشر...');
    speakText('سأبدأ الآن بالتعليق الصوتي المباشر على ما أراه على شاشتك');

    // تعليق فوري
    setTimeout(() => {
        const commentary = generateVoiceCommentary();
        addMessage('assistant', `تعليق مباشر: ${commentary}`);
        speakText(commentary);
    }, 2000);
}

// توليد تعليق صوتي
function generateVoiceCommentary() {
    const commentaries = [
        'أرى أنك تستخدم المساعد التقني الذكي. هذا تطبيق رائع للمساعدة في المهام التقنية',
        'الواجهة تبدو واضحة ومنظمة. يمكنك استخدام جميع الأدوات المتاحة بسهولة',
        'أرى أن هناك إمكانيات متعددة في هذا التطبيق. يمكنني مساعدتك في استكشافها',
        'الشاشة تظهر بيئة عمل تقنية متقدمة. أنا هنا لدعمك في أي مهمة تريد إنجازها',
        'يبدو أنك تعمل بكفاءة. إذا احتجت شرحاً لأي عنصر على الشاشة، فقط اسألني'
    ];

    return commentaries[Math.floor(Math.random() * commentaries.length)];
}

// تبديل صوت المساعد
function toggleAssistantVoice(button) {
    assistantVoiceEnabled = !assistantVoiceEnabled;

    if (assistantVoiceEnabled) {
        button.innerHTML = '<i class="fas fa-volume-up"></i> إيقاف الصوت';
        button.style.background = '#9C27B0';
        addMessage('assistant', 'تم تفعيل الصوت');
        speakText('تم تفعيل صوت المساعد المرئي');
    } else {
        button.innerHTML = '<i class="fas fa-volume-mute"></i> تفعيل الصوت';
        button.style.background = '#757575';
        addMessage('assistant', 'تم إيقاف الصوت');
    }
}

// إعدادات الصوت
function openVoiceSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// ===== ربط الأحداث =====

function initializeApp() {
    console.log('🚀 تهيئة التطبيق...');

    // ربط الأحداث
    bindEvents();

    // فحص الاتصال
    checkConnection();

    console.log('✅ تم تهيئة التطبيق');
}

function bindEvents() {
    // زر الإرسال
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        sendBtn.onclick = sendMessage;
    }

    // حقل الإدخال
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }

    // زر التسجيل الصوتي
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn) {
        voiceBtn.onclick = startVoiceRecording;
    }

    // أزرار الأدوات
    const screenShareBtn = document.getElementById('screenShareBtn');
    if (screenShareBtn) {
        screenShareBtn.onclick = startScreenShare;
    }

    const videoUploadBtn = document.getElementById('videoUploadBtn');
    if (videoUploadBtn) {
        videoUploadBtn.onclick = uploadFile;
    }

    const videoAnalyzeBtn = document.getElementById('videoAnalyzeBtn');
    if (videoAnalyzeBtn) {
        videoAnalyzeBtn.onclick = uploadFile;
    }

    const ar3dBtn = document.getElementById('ar3dBtn');
    if (ar3dBtn) {
        ar3dBtn.onclick = show3DView;
    }

    const summaryBtn = document.getElementById('summaryBtn');
    if (summaryBtn) {
        summaryBtn.onclick = generateSummary;
    }

    const voiceSettingsBtn = document.getElementById('voiceBtn');
    if (voiceSettingsBtn) {
        voiceSettingsBtn.onclick = openVoiceSettings;
    }

    // أزرار الإغلاق
    const closeDisplay = document.getElementById('closeDisplay');
    if (closeDisplay) {
        closeDisplay.onclick = () => {
            document.getElementById('displayArea').style.display = 'none';
        };
    }

    const closeSettingsBtn = document.getElementById('closeSettings');
    if (closeSettingsBtn) {
        closeSettingsBtn.onclick = closeSettings;
    }

    console.log('✅ تم ربط جميع الأحداث');
}

async function checkConnection() {
    try {
        const isConnected = await technicalAssistant.checkConnection();
        updateConnectionStatus(isConnected);
    } catch (error) {
        updateConnectionStatus(false);
    }
}

function updateConnectionStatus(isConnected) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-indicator span:last-child');

    if (statusDot && statusText) {
        if (isConnected) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }
}

// تصدير الوظائف
window.technicalAssistant = technicalAssistant;
window.sendMessage = sendMessage;
window.startVoiceRecording = startVoiceRecording;
window.startScreenShare = startScreenShare;
window.uploadFile = uploadFile;
window.show3DView = show3DView;
window.generateSummary = generateSummary;
window.openVoiceSettings = openVoiceSettings;
window.closeSettings = closeSettings;
window.initializeApp = initializeApp;
window.addMessage = addMessage;
window.speakText = speakText;
