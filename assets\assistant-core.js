// منطق المساعد الأساسي والذكاء الاصطناعي المحلي
// Core Assistant Logic and Local AI

// تعريف المتغيرات العامة
window.conversationHistory = window.conversationHistory || [];

class TechnicalAssistant {
    constructor() {
        this.MODEL = "deepseek-coder-6.7b-instruct";
        this.API_URL = "http://************:1234/v1/chat/completions";
        this.knowledgeBase = this.initializeKnowledgeBase();
        this.userPreferences = {};
        this.currentContext = {};
    }

    // تهيئة قاعدة المعرفة التقنية
    initializeKnowledgeBase() {
        return {
            programming: {
                javascript: {
                    basics: 'JavaScript هي لغة برمجة ديناميكية تستخدم في تطوير الويب',
                    functions: 'الدوال في JavaScript تُعرف باستخدام function أو =>'
                },
                python: {
                    basics: 'Python لغة برمجة عالية المستوى سهلة التعلم',
                    syntax: 'Python يستخدم المسافات البادئة لتحديد الكتل'
                },
                html: {
                    basics: 'HTML هي لغة ترميز لإنشاء صفحات الويب',
                    tags: 'HTML يستخدم العلامات (tags) لتنظيم المحتوى'
                },
                css: {
                    basics: 'CSS تستخدم لتنسيق وتصميم صفحات الويب',
                    selectors: 'CSS تستخدم المحددات لاستهداف عناصر HTML'
                }
            },
            networking: {
                basics: 'الشبكات تربط الأجهزة لتبادل البيانات',
                protocols: 'البروتوكولات مثل HTTP وTCP/IP تنظم التواصل'
            },
            databases: {
                sql: 'SQL لغة استعلام قواعد البيانات العلائقية',
                nosql: 'قواعد البيانات غير العلائقية مثل MongoDB'
            },
            security: {
                basics: 'أمن المعلومات يحمي البيانات من التهديدات',
                encryption: 'التشفير يحول البيانات إلى شكل غير مقروء'
            }
        };
    }

    // الاتصال مع LM Studio
    async askAssistant(userPrompt, systemPrompt = "") {
        try {
            console.log('🔄 إرسال طلب إلى LM Studio...');

            const body = {
                model: this.MODEL,
                messages: [
                    {
                        role: "system",
                        content: systemPrompt || "أنت مساعد تقني ذكي جدًا، قادر على تنفيذ أي أمر يُطلب منك بدقة. تستخدم أسلوبًا احترافيًا في الرد وتفهم كل ما يطلبه المستخدم، سواء كان طلب برمجة، شرح فيديو، توليد كائن 3D، أو أمر صوتي. اعتمد على فهمك الكامل للسياق وقدم نتائج فورية دقيقة، بأسلوب مختصر ومهني."
                    },
                    { role: "user", content: userPrompt }
                ],
                temperature: 0.7,
                max_tokens: 1000,
                stream: false
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 ثانية timeout

            const res = await fetch(this.API_URL, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                },
                body: JSON.stringify(body),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!res.ok) {
                const errorText = await res.text();
                console.error('خطأ من الخادم:', res.status, errorText);
                throw new Error(`خطأ HTTP ${res.status}: ${errorText}`);
            }

            const data = await res.json();

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error("استجابة غير صالحة من النموذج");
            }

            const response = data.choices[0].message.content.trim();
            console.log('✅ تم استلام الرد من LM Studio');
            return response;

        } catch (err) {
            console.error("خطأ في الاتصال مع المساعد:", err);

            if (err.name === 'AbortError') {
                return "⚠️ انتهت مهلة الاتصال مع النموذج. يرجى المحاولة مرة أخرى.";
            } else if (err.message.includes('fetch')) {
                return "⚠️ لا يمكن الاتصال بـ LM Studio. تأكد من:\n• تشغيل LM Studio\n• تحميل نموذج\n• تشغيل الخادم على المنفذ 1234";
            } else {
                return `⚠️ حدث خطأ: ${err.message}`;
            }
        }
    }

    // فحص حالة الاتصال مع LM Studio
    async checkConnection() {
        try {
            const response = await fetch(this.API_URL.replace('/chat/completions', '/models'), {
                method: 'GET',
                headers: { 'Accept': 'application/json' }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('✅ LM Studio متصل، النماذج المتاحة:', data.data?.map(m => m.id) || []);
                return true;
            }
            return false;
        } catch (error) {
            console.log('❌ LM Studio غير متصل:', error.message);
            return false;
        }
    }

    // تحليل السؤال وتحديد النوع
    analyzeQuestion(question) {
        const lowerQuestion = question.toLowerCase();

        // تحديد الموضوع
        let topic = 'general';
        let subtopic = null;
        let questionType = 'explanation';

        // تحليل الموضوع
        if (this.containsKeywords(lowerQuestion, ['javascript', 'js', 'جافا سكريبت'])) {
            topic = 'programming';
            subtopic = 'javascript';
        } else if (this.containsKeywords(lowerQuestion, ['python', 'بايثون'])) {
            topic = 'programming';
            subtopic = 'python';
        } else if (this.containsKeywords(lowerQuestion, ['html', 'إتش تي إم إل'])) {
            topic = 'programming';
            subtopic = 'html';
        } else if (this.containsKeywords(lowerQuestion, ['css', 'سي إس إس'])) {
            topic = 'programming';
            subtopic = 'css';
        } else if (this.containsKeywords(lowerQuestion, ['شبكة', 'network', 'إنترنت'])) {
            topic = 'networking';
        } else if (this.containsKeywords(lowerQuestion, ['قاعدة بيانات', 'database', 'sql'])) {
            topic = 'databases';
        } else if (this.containsKeywords(lowerQuestion, ['أمان', 'security', 'تشفير'])) {
            topic = 'security';
        }

        // تحديد نوع السؤال
        if (this.containsKeywords(lowerQuestion, ['كيف', 'how', 'شلون'])) {
            questionType = 'howto';
        } else if (this.containsKeywords(lowerQuestion, ['ما هو', 'what', 'شنو'])) {
            questionType = 'definition';
        } else if (this.containsKeywords(lowerQuestion, ['مثال', 'example', 'كود'])) {
            questionType = 'example';
        } else if (this.containsKeywords(lowerQuestion, ['مشكلة', 'خطأ', 'error', 'bug'])) {
            questionType = 'troubleshooting';
        }

        return { topic, subtopic, questionType };
    }

    // فحص وجود كلمات مفتاحية
    containsKeywords(text, keywords) {
        return keywords.some(keyword => text.includes(keyword));
    }

    // الحصول على إجابة شاملة
    async getResponse(question) {
        try {
            // محاولة الحصول على رد من LM Studio أولاً
            const aiResponse = await this.askAssistant(question);

            // إذا كان الرد من AI صالحاً، استخدمه
            if (aiResponse && !aiResponse.includes('⚠️ حدث خطأ')) {
                this.updateContext(question, aiResponse);
                return aiResponse;
            }

            // إذا فشل AI، استخدم المعرفة المحلية
            const analysis = this.analyzeQuestion(question);
            const localResponse = this.generateLocalResponse(question, analysis);
            this.updateContext(question, localResponse);
            return localResponse;

        } catch (error) {
            console.error('خطأ في getResponse:', error);
            return 'عذراً، حدث خطأ في معالجة سؤالك. يرجى المحاولة مرة أخرى.';
        }
    }

    // توليد رد محلي
    generateLocalResponse(question, analysis) {
        const { topic, subtopic, questionType } = analysis;

        // الحصول على المعلومات من قاعدة المعرفة
        const knowledge = this.getKnowledge(topic, subtopic);

        if (knowledge) {
            return `بناءً على معرفتي المحلية:\n\n${knowledge.basics || knowledge}\n\nهل تريد المزيد من التفاصيل؟`;
        }

        return `أفهم سؤالك حول "${question}". دعني أساعدك بأفضل ما أستطيع بناءً على معرفتي التقنية.`;
    }

    // الحصول على المعرفة من قاعدة البيانات
    getKnowledge(topic, subtopic) {
        if (subtopic && this.knowledgeBase[topic] && this.knowledgeBase[topic][subtopic]) {
            return this.knowledgeBase[topic][subtopic];
        } else if (this.knowledgeBase[topic]) {
            return this.knowledgeBase[topic];
        }
        return null;
    }

    // حفظ السياق للمحادثة
    updateContext(question, response) {
        this.currentContext = {
            lastQuestion: question,
            lastResponse: response,
            timestamp: new Date().toISOString()
        };

        this.conversationHistory.push({
            question: question,
            response: response,
            timestamp: new Date().toISOString()
        });
    }
}

// إنشاء مثيل المساعد التقني
const technicalAssistant = new TechnicalAssistant();

// ===== وظائف الواجهة الرئيسية =====

// إرسال رسالة وعرض الرد
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    appendMessage('user', message);
    messageInput.value = '';

    // إظهار مؤشر الكتابة
    showTypingIndicator();

    try {
        // الحصول على الرد من النموذج
        const response = await technicalAssistant.getResponse(message);

        // إزالة مؤشر الكتابة
        hideTypingIndicator();

        // عرض رد المساعد
        appendMessage('assistant', response);

        // تشغيل الرد صوتياً
        if (window.speechSettings && speechSettings.enabled) {
            speakText(response);
        }

    } catch (error) {
        hideTypingIndicator();
        appendMessage('assistant', 'عذراً، حدث خطأ في معالجة طلبك.');
        console.error('خطأ في إرسال الرسالة:', error);
    }
}

// عرض الرسالة في واجهة المستخدم
function appendMessage(role, text) {
    const container = document.getElementById("chatContainer");
    if (!container) return;

    const msgDiv = document.createElement("div");
    msgDiv.className = `message ${role}`;

    const avatar = document.createElement("div");
    avatar.className = "avatar";
    avatar.innerHTML = `<i class="fas ${role === "user" ? "fa-user" : "fa-robot"}"></i>`;

    const messageContent = document.createElement("div");
    messageContent.className = "message-content";

    // معالجة النص لدعم التنسيق
    const formattedText = formatMessageText(text);
    messageContent.innerHTML = formattedText;

    msgDiv.appendChild(avatar);
    msgDiv.appendChild(messageContent);
    container.appendChild(msgDiv);

    // التمرير للأسفل
    container.scrollTop = container.scrollHeight;

    // حفظ في تاريخ المحادثة
    if (!window.conversationHistory) {
        window.conversationHistory = [];
    }
    window.conversationHistory.push({
        role: role,
        content: text,
        timestamp: new Date().toISOString()
    });
}

// تنسيق النص لدعم Markdown البسيط
function formatMessageText(text) {
    return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        .replace(/\n/g, '<br>');
}

// إظهار مؤشر الكتابة
function showTypingIndicator() {
    const container = document.getElementById("chatContainer");
    if (!container) return;

    // إزالة مؤشر سابق إن وجد
    hideTypingIndicator();

    const indicator = document.createElement("div");
    indicator.className = "message assistant typing-indicator";
    indicator.id = "typingIndicator";
    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;

    container.appendChild(indicator);
    container.scrollTop = container.scrollHeight;
}

// إخفاء مؤشر الكتابة
function hideTypingIndicator() {
    const indicator = document.getElementById("typingIndicator");
    if (indicator) {
        indicator.remove();
    }
}

// ===== وظائف الصوت =====

// إعدادات الصوت
window.speechSettings = {
    enabled: true,
    language: 'ar-SA',
    rate: 1,
    volume: 0.8,
    pitch: 1
};

// تحويل النص إلى كلام
function speakText(text) {
    if (!text || !speechSettings.enabled) return;

    // تنظيف النص
    const cleanText = text.replace(/[*#`]/g, '').replace(/\n+/g, ' ').trim();

    if ('speechSynthesis' in window) {
        // إيقاف أي كلام سابق
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(cleanText);
        utterance.lang = speechSettings.language;
        utterance.rate = speechSettings.rate;
        utterance.volume = speechSettings.volume;
        utterance.pitch = speechSettings.pitch;

        // البحث عن صوت عربي
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(voice =>
            voice.lang.startsWith('ar') ||
            voice.name.toLowerCase().includes('arabic')
        );

        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }

        utterance.onstart = () => console.log('🔊 بدء النطق');
        utterance.onend = () => console.log('✅ انتهاء النطق');
        utterance.onerror = (e) => console.error('❌ خطأ في النطق:', e);

        speechSynthesis.speak(utterance);
    }
}

// تسجيل الصوت وتحويله إلى نص
let recognition = null;
let isListening = false;

function startVoiceRecording() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('المتصفح لا يدعم التعرف على الكلام');
        return;
    }

    if (isListening) {
        stopVoiceRecording();
        return;
    }

    recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
    recognition.lang = speechSettings.language;
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.maxAlternatives = 1;

    recognition.onstart = () => {
        isListening = true;
        updateVoiceButton(true);
        console.log('🎤 بدء التسجيل الصوتي');
    };

    recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        console.log('🗣️ النص المسجل:', transcript);

        // وضع النص في حقل الإدخال
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
            messageInput.value = transcript;

            // إرسال الرسالة تلقائياً
            setTimeout(() => sendMessage(), 500);
        }
    };

    recognition.onerror = (event) => {
        console.error('❌ خطأ في التسجيل:', event.error);
        stopVoiceRecording();

        let errorMsg = 'حدث خطأ في التسجيل الصوتي';
        if (event.error === 'not-allowed') {
            errorMsg = 'يرجى السماح بالوصول إلى الميكروفون';
        }
        appendMessage('assistant', errorMsg);
    };

    recognition.onend = () => {
        stopVoiceRecording();
    };

    recognition.start();
}

function stopVoiceRecording() {
    if (recognition) {
        recognition.stop();
        recognition = null;
    }
    isListening = false;
    updateVoiceButton(false);
    console.log('⏹️ توقف التسجيل الصوتي');
}

function updateVoiceButton(recording) {
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn) {
        if (recording) {
            voiceBtn.classList.add('recording');
            voiceBtn.title = 'جاري التسجيل... انقر للتوقف';
        } else {
            voiceBtn.classList.remove('recording');
            voiceBtn.title = 'اضغط للتحدث';
        }
    }
}

// ===== وظائف الأدوات =====

// مشاركة الشاشة
async function startScreenShare() {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        appendMessage('assistant', 'عذراً، المتصفح لا يدعم مشاركة الشاشة');
        return;
    }

    try {
        appendMessage('assistant', 'جاري تفعيل مشاركة الشاشة...');

        const stream = await navigator.mediaDevices.getDisplayMedia({
            video: { mediaSource: 'screen' },
            audio: true
        });

        displaySharedScreen(stream);
        appendMessage('assistant', 'تم بدء مشاركة الشاشة بنجاح!');

    } catch (error) {
        console.error('خطأ في مشاركة الشاشة:', error);
        let errorMessage = 'حدث خطأ في مشاركة الشاشة';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن لمشاركة الشاشة';
        }

        appendMessage('assistant', errorMessage);
    }
}

function displaySharedScreen(stream) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    if (!displayArea || !displayContent || !displayTitle) return;

    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';

    const controls = document.createElement('div');
    controls.style.marginTop = '15px';
    controls.style.textAlign = 'center';

    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
    stopBtn.className = 'tool-btn';
    stopBtn.style.background = '#ff4757';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        displayArea.style.display = 'none';
        appendMessage('assistant', 'تم إيقاف مشاركة الشاشة');
    };

    controls.appendChild(stopBtn);

    displayTitle.textContent = 'مشاركة الشاشة';
    displayContent.innerHTML = '';
    displayContent.appendChild(video);
    displayContent.appendChild(controls);
    displayArea.style.display = 'flex';

    // مراقبة إيقاف المشاركة
    stream.getVideoTracks()[0].addEventListener('ended', () => {
        displayArea.style.display = 'none';
        appendMessage('assistant', 'تم إيقاف مشاركة الشاشة');
    });
}

// رفع الملفات
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    if (!fileInput) return;

    fileInput.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileUpload(file);
        }
    };
    fileInput.click();
}

function handleFileUpload(file) {
    appendMessage('assistant', `تم تحميل الملف: ${file.name}`);

    if (file.type.startsWith('video/')) {
        handleVideoFile(file);
    } else if (file.type.startsWith('image/')) {
        handleImageFile(file);
    } else if (file.type.startsWith('audio/')) {
        handleAudioFile(file);
    } else {
        handleDocumentFile(file);
    }
}

function handleVideoFile(file) {
    const video = document.createElement('video');
    const url = URL.createObjectURL(file);
    video.src = url;
    video.controls = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';

    video.onloadedmetadata = () => {
        const info = `
📹 **معلومات الفيديو:**
• الاسم: ${file.name}
• الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB
• المدة: ${formatDuration(video.duration)}
• الأبعاد: ${video.videoWidth} × ${video.videoHeight}
        `;

        appendMessage('assistant', info);
        displayInArea('عرض الفيديو', video);
        URL.revokeObjectURL(url);
    };
}

function handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = document.createElement('img');
        img.src = e.target.result;
        img.style.maxWidth = '100%';
        img.style.borderRadius = '8px';

        appendMessage('assistant', `تم تحميل الصورة: ${file.name}`);
        displayInArea('عرض الصورة', img);
    };
    reader.readAsDataURL(file);
}

function handleAudioFile(file) {
    const audio = document.createElement('audio');
    const url = URL.createObjectURL(file);
    audio.src = url;
    audio.controls = true;
    audio.style.width = '100%';

    appendMessage('assistant', `تم تحميل الملف الصوتي: ${file.name}`);
    displayInArea('تشغيل الصوت', audio);
    URL.revokeObjectURL(url);
}

function handleDocumentFile(file) {
    const info = `
📄 **معلومات المستند:**
• الاسم: ${file.name}
• الحجم: ${(file.size / 1024).toFixed(2)} KB
• النوع: ${file.type || 'غير محدد'}
    `;

    appendMessage('assistant', info);
}

function displayInArea(title, element) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    if (!displayArea || !displayContent || !displayTitle) return;

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.marginTop = '15px';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
    };

    displayTitle.textContent = title;
    displayContent.innerHTML = '';
    displayContent.appendChild(element);
    displayContent.appendChild(closeBtn);
    displayArea.style.display = 'flex';
}

function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

// العرض ثلاثي الأبعاد
function show3DView() {
    if (!window.THREE) {
        appendMessage('assistant', 'مكتبة Three.js غير متاحة للعرض ثلاثي الأبعاد');
        return;
    }

    appendMessage('assistant', 'جاري تحميل العرض ثلاثي الأبعاد...');

    try {
        create3DScene();
        appendMessage('assistant', 'تم تفعيل العرض ثلاثي الأبعاد! استخدم الماوس للتفاعل.');
    } catch (error) {
        console.error('خطأ في العرض ثلاثي الأبعاد:', error);
        appendMessage('assistant', 'حدث خطأ في تحميل العرض ثلاثي الأبعاد');
    }
}

function create3DScene() {
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);

    const camera = new THREE.PerspectiveCamera(75, 400 / 300, 0.1, 1000);
    camera.position.set(5, 5, 5);

    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(400, 300);
    renderer.shadowMap.enabled = true;

    // إضافة الإضاءة
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // إنشاء مكعب ملون
    const geometry = new THREE.BoxGeometry(2, 2, 2);
    const materials = [
        new THREE.MeshLambertMaterial({ color: 0xff0000 }),
        new THREE.MeshLambertMaterial({ color: 0x00ff00 }),
        new THREE.MeshLambertMaterial({ color: 0x0000ff }),
        new THREE.MeshLambertMaterial({ color: 0xffff00 }),
        new THREE.MeshLambertMaterial({ color: 0xff00ff }),
        new THREE.MeshLambertMaterial({ color: 0x00ffff })
    ];

    const cube = new THREE.Mesh(geometry, materials);
    cube.castShadow = true;
    scene.add(cube);

    // إضافة أرضية
    const floorGeometry = new THREE.PlaneGeometry(20, 20);
    const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.position.y = -2;
    floor.receiveShadow = true;
    scene.add(floor);

    // أزرار التحكم
    const controlsDiv = document.createElement('div');
    controlsDiv.style.marginTop = '15px';
    controlsDiv.style.textAlign = 'center';

    let autoRotate = false;

    const rotateBtn = document.createElement('button');
    rotateBtn.innerHTML = '<i class="fas fa-sync"></i> دوران تلقائي';
    rotateBtn.className = 'tool-btn';
    rotateBtn.style.margin = '5px';
    rotateBtn.onclick = () => {
        autoRotate = !autoRotate;
        rotateBtn.innerHTML = autoRotate ?
            '<i class="fas fa-pause"></i> إيقاف الدوران' :
            '<i class="fas fa-sync"></i> دوران تلقائي';
    };

    const colorBtn = document.createElement('button');
    colorBtn.innerHTML = '<i class="fas fa-palette"></i> تغيير الألوان';
    colorBtn.className = 'tool-btn';
    colorBtn.style.margin = '5px';
    colorBtn.onclick = () => {
        const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff];
        cube.material.forEach(material => {
            material.color.setHex(colors[Math.floor(Math.random() * colors.length)]);
        });
    };

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.margin = '5px';
    closeBtn.onclick = () => {
        document.getElementById('displayArea').style.display = 'none';
        renderer.dispose();
    };

    controlsDiv.appendChild(rotateBtn);
    controlsDiv.appendChild(colorBtn);
    controlsDiv.appendChild(closeBtn);

    // عرض المشهد
    const container = document.createElement('div');
    container.appendChild(renderer.domElement);
    container.appendChild(controlsDiv);

    displayInArea('العرض ثلاثي الأبعاد', container);

    // حلقة العرض
    function animate() {
        requestAnimationFrame(animate);

        if (autoRotate) {
            cube.rotation.y += 0.01;
            cube.rotation.x += 0.005;
        }

        camera.lookAt(cube.position);
        renderer.render(scene, camera);
    }

    animate();

    // تفاعل الماوس
    let mouseDown = false;
    let mouseX = 0, mouseY = 0;

    renderer.domElement.addEventListener('mousedown', (e) => {
        mouseDown = true;
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    renderer.domElement.addEventListener('mouseup', () => {
        mouseDown = false;
    });

    renderer.domElement.addEventListener('mousemove', (e) => {
        if (!mouseDown) return;

        const deltaX = e.clientX - mouseX;
        const deltaY = e.clientY - mouseY;

        cube.rotation.y += deltaX * 0.01;
        cube.rotation.x += deltaY * 0.01;

        mouseX = e.clientX;
        mouseY = e.clientY;
    });
}

// توليد ملخص
function generateSummary() {
    if (!window.conversationHistory || window.conversationHistory.length === 0) {
        appendMessage('assistant', 'لا توجد محادثة لتلخيصها');
        return;
    }

    appendMessage('assistant', 'جاري توليد ملخص المحادثة...');

    const summary = createConversationSummary();
    displaySummary(summary);

    appendMessage('assistant', 'تم إنشاء ملخص المحادثة! راجعه في منطقة العرض.');
}

function createConversationSummary() {
    const userMessages = window.conversationHistory.filter(msg => msg.role === 'user');
    const assistantMessages = window.conversationHistory.filter(msg => msg.role === 'assistant');

    let summary = `📊 **ملخص المحادثة**\n\n`;
    summary += `📈 **إحصائيات:**\n`;
    summary += `• إجمالي الرسائل: ${window.conversationHistory.length}\n`;
    summary += `• رسائل المستخدم: ${userMessages.length}\n`;
    summary += `• رسائل المساعد: ${assistantMessages.length}\n\n`;

    if (userMessages.length > 0) {
        summary += `❓ **أهم الأسئلة:**\n`;
        userMessages.slice(-5).forEach((msg, index) => {
            const question = msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : '');
            summary += `${index + 1}. ${question}\n`;
        });
        summary += '\n';
    }

    summary += `💡 **التوصيات:**\n`;
    summary += `• حفظ المحادثة للمراجعة\n`;
    summary += `• مراجعة النقاط المهمة\n`;
    summary += `• طرح أسئلة إضافية عند الحاجة\n`;

    return summary;
}

function displaySummary(summary) {
    const summaryDiv = document.createElement('div');
    summaryDiv.style.padding = '20px';
    summaryDiv.style.lineHeight = '1.6';

    const formattedSummary = formatMessageText(summary);
    summaryDiv.innerHTML = formattedSummary;

    const controlsDiv = document.createElement('div');
    controlsDiv.style.marginTop = '20px';
    controlsDiv.style.textAlign = 'center';

    const copyBtn = document.createElement('button');
    copyBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ';
    copyBtn.className = 'tool-btn';
    copyBtn.style.margin = '5px';
    copyBtn.onclick = () => {
        navigator.clipboard.writeText(summary).then(() => {
            appendMessage('assistant', 'تم نسخ الملخص');
        });
    };

    const saveBtn = document.createElement('button');
    saveBtn.innerHTML = '<i class="fas fa-download"></i> حفظ';
    saveBtn.className = 'tool-btn';
    saveBtn.style.margin = '5px';
    saveBtn.onclick = () => {
        const blob = new Blob([summary], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ملخص_المحادثة_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);
        appendMessage('assistant', 'تم حفظ الملخص');
    };

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.margin = '5px';
    closeBtn.onclick = () => {
        document.getElementById('displayArea').style.display = 'none';
    };

    controlsDiv.appendChild(copyBtn);
    controlsDiv.appendChild(saveBtn);
    controlsDiv.appendChild(closeBtn);
    summaryDiv.appendChild(controlsDiv);

    displayInArea('ملخص المحادثة', summaryDiv);
}

// إعدادات الصوت
function openVoiceSettings() {
    const modal = document.getElementById('settingsModal');
    if (!modal) return;

    modal.style.display = 'flex';

    // ربط الإعدادات
    const voiceLanguage = document.getElementById('voiceLanguage');
    const speechRate = document.getElementById('speechRate');
    const speechVolume = document.getElementById('speechVolume');

    if (voiceLanguage) {
        voiceLanguage.value = speechSettings.language;
        voiceLanguage.onchange = () => {
            speechSettings.language = voiceLanguage.value;
            saveSettings();
        };
    }

    if (speechRate) {
        speechRate.value = speechSettings.rate;
        speechRate.oninput = () => {
            speechSettings.rate = parseFloat(speechRate.value);
            saveSettings();
        };
    }

    if (speechVolume) {
        speechVolume.value = speechSettings.volume;
        speechVolume.oninput = () => {
            speechSettings.volume = parseFloat(speechVolume.value);
            saveSettings();
        };
    }
}

function closeSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function closeDisplayArea() {
    const displayArea = document.getElementById('displayArea');
    if (displayArea) {
        displayArea.style.display = 'none';
    }
}

// حفظ وتحميل الإعدادات
function saveSettings() {
    try {
        localStorage.setItem('speechSettings', JSON.stringify(speechSettings));
    } catch (e) {
        console.error('خطأ في حفظ الإعدادات:', e);
    }
}

function loadSettings() {
    try {
        const saved = localStorage.getItem('speechSettings');
        if (saved) {
            const savedSettings = JSON.parse(saved);
            speechSettings = { ...speechSettings, ...savedSettings };
        }
    } catch (e) {
        console.error('خطأ في تحميل الإعدادات:', e);
    }
}

// ===== ربط الأحداث =====

function initializeApp() {
    console.log('🚀 تهيئة التطبيق...');

    // تحميل الإعدادات
    loadSettings();

    // ربط الأحداث
    bindAllEvents();

    // فحص الاتصال مع LM Studio
    checkLMStudioConnection();

    // تحميل الأصوات المتاحة
    if (window.speechSynthesis) {
        speechSynthesis.onvoiceschanged = () => {
            console.log('🔊 تم تحميل الأصوات المتاحة');
        };
    }

    console.log('✅ تم تهيئة التطبيق بنجاح');
}

function bindAllEvents() {
    // زر الإرسال
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        sendBtn.onclick = sendMessage;
    }

    // حقل الإدخال - Enter
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }

    // زر التسجيل الصوتي
    const voiceRecordBtn = document.getElementById('voiceRecordBtn');
    if (voiceRecordBtn) {
        voiceRecordBtn.onclick = startVoiceRecording;
    }

    // أزرار الأدوات
    const screenShareBtn = document.getElementById('screenShareBtn');
    if (screenShareBtn) {
        screenShareBtn.onclick = startScreenShare;
    }

    const videoUploadBtn = document.getElementById('videoUploadBtn');
    if (videoUploadBtn) {
        videoUploadBtn.onclick = uploadFile;
    }

    const videoAnalyzeBtn = document.getElementById('videoAnalyzeBtn');
    if (videoAnalyzeBtn) {
        videoAnalyzeBtn.onclick = uploadFile;
    }

    const ar3dBtn = document.getElementById('ar3dBtn');
    if (ar3dBtn) {
        ar3dBtn.onclick = show3DView;
    }

    const summaryBtn = document.getElementById('summaryBtn');
    if (summaryBtn) {
        summaryBtn.onclick = generateSummary;
    }

    const voiceBtn = document.getElementById('voiceBtn');
    if (voiceBtn) {
        voiceBtn.onclick = openVoiceSettings;
    }

    // أزرار الإغلاق
    const closeDisplay = document.getElementById('closeDisplay');
    if (closeDisplay) {
        closeDisplay.onclick = closeDisplayArea;
    }

    const closeSettings = document.getElementById('closeSettings');
    if (closeSettings) {
        closeSettings.onclick = closeSettings;
    }

    // منطقة السحب والإفلات
    const fileDropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');

    if (fileDropZone && fileInput) {
        fileDropZone.onclick = () => fileInput.click();

        fileDropZone.ondragover = (e) => {
            e.preventDefault();
            fileDropZone.classList.add('dragover');
        };

        fileDropZone.ondragleave = () => {
            fileDropZone.classList.remove('dragover');
        };

        fileDropZone.ondrop = (e) => {
            e.preventDefault();
            fileDropZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        };

        fileInput.onchange = (e) => {
            if (e.target.files.length > 0) {
                handleFileUpload(e.target.files[0]);
            }
        };
    }

    console.log('✅ تم ربط جميع الأحداث');
}

async function checkLMStudioConnection() {
    try {
        const isConnected = await technicalAssistant.checkConnection();
        updateConnectionStatus(isConnected);

        if (isConnected) {
            console.log('✅ متصل بـ LM Studio');
        } else {
            console.log('⚠️ غير متصل بـ LM Studio');
        }
    } catch (error) {
        console.error('خطأ في فحص الاتصال:', error);
        updateConnectionStatus(false);
    }
}

function updateConnectionStatus(isConnected) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-indicator span:last-child');

    if (statusDot && statusText) {
        if (isConnected) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }
}

// تصدير الوظائف للاستخدام العام
window.technicalAssistant = technicalAssistant;
window.sendMessage = sendMessage;
window.startVoiceRecording = startVoiceRecording;
window.startScreenShare = startScreenShare;
window.uploadFile = uploadFile;
window.show3DView = show3DView;
window.generateSummary = generateSummary;
window.openVoiceSettings = openVoiceSettings;
window.closeSettings = closeSettings;
window.closeDisplayArea = closeDisplayArea;
window.speakText = speakText;
window.initializeApp = initializeApp;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeApp);
