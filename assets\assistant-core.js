// المساعد التقني الذكي - ملف أساسي مبسط

// متغيرات عامة
window.conversationHistory = [];
let isListening = false;

// إعدادات الصوت المحسنة
const speechSettings = {
    enabled: true,
    language: 'ar-SA',
    rate: 0.9,
    volume: 1.0,
    pitch: 1.1,
    voiceName: 'Arabic Female',
    conversationMode: true,
    autoResponse: true
};

// متغيرات المحادثة الصوتية
let isInConversation = false;
let conversationContext = '';
let lastUserSpeech = '';
let voiceRecognition = null;
let isContinuousListening = false;

// فئة المساعد التقني
class TechnicalAssistant {
    constructor() {
        this.API_URL = "http://localhost:1234/v1/chat/completions";
        this.MODEL = "deepseek-coder-6.7b-instruct";
        console.log('🤖 تم إنشاء المساعد التقني');
    }

    // الحصول على رد من النموذج
    async getResponse(userMessage) {
        try {
            console.log('📤 إرسال رسالة:', userMessage);
            
            const response = await fetch(this.API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.MODEL,
                    messages: [
                        {
                            role: "system",
                            content: "أنت مساعد تقني ذكي. أجب بالعربية بطريقة واضحة ومفيدة."
                        },
                        {
                            role: "user",
                            content: userMessage
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.choices[0].message.content;
            
            console.log('📥 تم استلام الرد');
            return aiResponse;

        } catch (error) {
            console.error('❌ خطأ:', error);
            return this.getLocalResponse(userMessage);
        }
    }

    // رد محلي في حالة عدم توفر الاتصال
    getLocalResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {
            return 'أهلاً وسهلاً! أنا المساعد التقني الذكي. كيف يمكنني مساعدتك؟';
        }
        
        if (lowerMessage.includes('javascript')) {
            return 'JavaScript هي لغة برمجة قوية تُستخدم في تطوير الويب. هل تريد معرفة شيء محدد عنها؟';
        }
        
        if (lowerMessage.includes('python')) {
            return 'Python لغة برمجة سهلة التعلم ومتعددة الاستخدامات. ما الذي تريد معرفته عنها؟';
        }
        
        return 'شكراً لسؤالك. لا يمكنني الاتصال بالنموذج حالياً، لكن يمكنني مساعدتك في الأسئلة التقنية الأساسية.';
    }

    // فحص الاتصال
    async checkConnection() {
        try {
            const response = await fetch(this.API_URL.replace('/chat/completions', '/models'));
            return response.ok;
        } catch {
            return false;
        }
    }
}

// إنشاء مثيل المساعد
const technicalAssistant = new TechnicalAssistant();

// ===== وظائف الواجهة =====

// إرسال رسالة
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    addMessage('user', message);
    messageInput.value = '';

    // إظهار مؤشر الكتابة
    showTyping();

    try {
        // الحصول على الرد
        const response = await technicalAssistant.getResponse(message);
        
        // إخفاء مؤشر الكتابة
        hideTyping();
        
        // إضافة رد المساعد
        addMessage('assistant', response);

        // قراءة الرد صوتياً
        if (speechSettings.enabled) {
            speakText(response);
        }

    } catch (error) {
        hideTyping();
        addMessage('assistant', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
        console.error('خطأ:', error);
    }
}

// إضافة رسالة للمحادثة
function addMessage(role, content) {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.innerHTML = `<i class="fas ${role === 'user' ? 'fa-user' : 'fa-robot'}"></i>`;
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    container.appendChild(messageDiv);
    
    // التمرير للأسفل
    container.scrollTop = container.scrollHeight;

    // حفظ في التاريخ
    window.conversationHistory.push({
        role: role,
        content: content,
        timestamp: new Date().toISOString()
    });
}

// مؤشر الكتابة
function showTyping() {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    hideTyping(); // إزالة أي مؤشر سابق

    const indicator = document.createElement('div');
    indicator.className = 'message assistant typing-indicator';
    indicator.id = 'typingIndicator';
    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    container.appendChild(indicator);
    container.scrollTop = container.scrollHeight;
}

function hideTyping() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// ===== وظائف الصوت =====

// تحويل النص إلى كلام محسن
function speakText(text, options = {}) {
    if (!text || !speechSettings.enabled) return;

    // تنظيف وتحسين النص
    let cleanText = text
        .replace(/[*#`]/g, '')
        .replace(/\n+/g, '. ')
        .replace(/\.\./g, '.')
        .replace(/([.!?])\s*([.!?])/g, '$1 ')
        .trim();

    // إضافة توقفات طبيعية
    cleanText = cleanText
        .replace(/([،,])/g, '$1 ')
        .replace(/([.!?])/g, '$1 ')
        .replace(/\s+/g, ' ');

    console.log('🗣️ النطق:', cleanText);

    // محاولة استخدام ResponsiveVoice أولاً للحصول على جودة أفضل
    if (typeof responsiveVoice !== 'undefined' && responsiveVoice.voiceSupport()) {
        const voiceOptions = {
            rate: options.rate || speechSettings.rate,
            volume: options.volume || speechSettings.volume,
            pitch: options.pitch || speechSettings.pitch,
            onstart: () => {
                console.log('🔊 بدء النطق المحسن');
                if (options.onStart) options.onStart();
            },
            onend: () => {
                console.log('✅ انتهاء النطق');
                if (options.onEnd) options.onEnd();

                // في وضع المحادثة، ابدأ الاستماع تلقائياً
                if (speechSettings.conversationMode && isInConversation) {
                    setTimeout(() => startContinuousListening(), 1000);
                }
            },
            onerror: (error) => {
                console.error('❌ خطأ في ResponsiveVoice:', error);
                fallbackToSpeechSynthesis(cleanText, options);
            }
        };

        // اختيار الصوت المناسب
        let voiceName = speechSettings.voiceName;
        if (speechSettings.language === 'ar-SA' || speechSettings.language === 'ar-IQ') {
            voiceName = 'Arabic Female';
        }

        responsiveVoice.speak(cleanText, voiceName, voiceOptions);
    } else {
        fallbackToSpeechSynthesis(cleanText, options);
    }
}

// وظيفة بديلة محسنة لـ speechSynthesis
function fallbackToSpeechSynthesis(text, options = {}) {
    if ('speechSynthesis' in window) {
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = speechSettings.language;
        utterance.rate = options.rate || speechSettings.rate;
        utterance.volume = options.volume || speechSettings.volume;
        utterance.pitch = options.pitch || speechSettings.pitch || 1.1;

        // البحث عن أفضل صوت عربي متاح
        const voices = speechSynthesis.getVoices();
        const arabicVoices = voices.filter(voice =>
            voice.lang.startsWith('ar') ||
            voice.name.toLowerCase().includes('arabic') ||
            voice.name.toLowerCase().includes('female')
        );

        if (arabicVoices.length > 0) {
            // اختيار أفضل صوت أنثوي عربي
            const femaleArabicVoice = arabicVoices.find(voice =>
                voice.name.toLowerCase().includes('female')
            ) || arabicVoices[0];

            utterance.voice = femaleArabicVoice;
            console.log('🔊 استخدام الصوت:', femaleArabicVoice.name);
        }

        utterance.onstart = () => {
            console.log('🔊 بدء النطق (SpeechSynthesis)');
            if (options.onStart) options.onStart();
        };

        utterance.onend = () => {
            console.log('✅ انتهاء النطق');
            if (options.onEnd) options.onEnd();

            // في وضع المحادثة، ابدأ الاستماع تلقائياً
            if (speechSettings.conversationMode && isInConversation) {
                setTimeout(() => startContinuousListening(), 1000);
            }
        };

        utterance.onerror = (event) => {
            console.error('❌ خطأ في النطق:', event.error);
            if (options.onError) options.onError(event);
        };

        speechSynthesis.speak(utterance);
    }
}

// ===== نظام المحادثة الصوتية التفاعلية =====

// بدء المحادثة الصوتية التفاعلية
function startVoiceConversation() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        addMessage('assistant', 'عذراً، المتصفح لا يدعم التعرف على الكلام');
        speakText('عذراً، المتصفح لا يدعم التعرف على الكلام');
        return;
    }

    isInConversation = true;
    speechSettings.conversationMode = true;

    addMessage('assistant', 'تم تفعيل وضع المحادثة الصوتية التفاعلية! تحدث معي بطبيعية');
    speakText('مرحباً! أنا مساعدك الصوتي التفاعلي. يمكنك التحدث معي بطبيعية وسأرد عليك صوتياً. ما الذي تريد أن نتحدث عنه؟', {
        onEnd: () => {
            setTimeout(() => startContinuousListening(), 1500);
        }
    });

    updateVoiceButton(true);
}

// إيقاف المحادثة الصوتية
function stopVoiceConversation() {
    isInConversation = false;
    speechSettings.conversationMode = false;
    isContinuousListening = false;

    if (voiceRecognition) {
        voiceRecognition.stop();
        voiceRecognition = null;
    }

    updateVoiceButton(false);
    addMessage('assistant', 'تم إيقاف وضع المحادثة الصوتية');
    speakText('تم إيقاف وضع المحادثة الصوتية. شكراً لك على المحادثة الرائعة!');
}

// بدء الاستماع المستمر
function startContinuousListening() {
    if (!isInConversation || isContinuousListening) return;

    voiceRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
    voiceRecognition.lang = speechSettings.language;
    voiceRecognition.continuous = true;
    voiceRecognition.interimResults = true;
    voiceRecognition.maxAlternatives = 3;

    let finalTranscript = '';
    let interimTranscript = '';

    voiceRecognition.onstart = () => {
        isContinuousListening = true;
        console.log('🎤 بدء الاستماع المستمر');
        updateListeningIndicator(true);
    };

    voiceRecognition.onresult = (event) => {
        finalTranscript = '';
        interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }

        // عرض النص المؤقت
        updateInterimText(interimTranscript);

        // معالجة النص النهائي
        if (finalTranscript.trim()) {
            processVoiceInput(finalTranscript.trim());
        }
    };

    voiceRecognition.onerror = (event) => {
        console.error('❌ خطأ في الاستماع:', event.error);
        if (event.error === 'no-speech') {
            // إعادة المحاولة بعد صمت
            setTimeout(() => {
                if (isInConversation) startContinuousListening();
            }, 2000);
        }
    };

    voiceRecognition.onend = () => {
        isContinuousListening = false;
        updateListeningIndicator(false);

        // إعادة بدء الاستماع إذا كنا في وضع المحادثة
        if (isInConversation) {
            setTimeout(() => startContinuousListening(), 1000);
        }
    };

    voiceRecognition.start();
}

// معالجة المدخل الصوتي
async function processVoiceInput(transcript) {
    lastUserSpeech = transcript;
    console.log('🗣️ المستخدم قال:', transcript);

    // إضافة رسالة المستخدم
    addMessage('user', transcript);

    // تحديث السياق
    conversationContext += `المستخدم: ${transcript}\n`;

    // تحليل نوع الطلب
    const requestType = analyzeVoiceRequest(transcript);

    // إظهار مؤشر التفكير
    showThinkingIndicator();

    try {
        let response = '';

        if (requestType.isCommand) {
            // تنفيذ أمر مباشر
            response = await executeVoiceCommand(requestType.command, transcript);
        } else {
            // محادثة عادية مع السياق
            const contextualPrompt = buildContextualPrompt(transcript);
            response = await technicalAssistant.getResponse(contextualPrompt);
        }

        // إزالة مؤشر التفكير
        hideThinkingIndicator();

        // إضافة رد المساعد
        addMessage('assistant', response);

        // تحديث السياق
        conversationContext += `المساعد: ${response}\n`;

        // الرد صوتياً
        speakText(response, {
            onEnd: () => {
                // الاستمرار في الاستماع
                if (isInConversation) {
                    setTimeout(() => startContinuousListening(), 1000);
                }
            }
        });

    } catch (error) {
        hideThinkingIndicator();
        const errorResponse = 'عذراً، حدث خطأ في فهم طلبك. يمكنك إعادة المحاولة';
        addMessage('assistant', errorResponse);
        speakText(errorResponse);
    }
}

// تحليل طلب صوتي
function analyzeVoiceRequest(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // أوامر مباشرة
    const commands = {
        'شارك الشاشة': 'screen_share',
        'افتح مشاركة الشاشة': 'screen_share',
        'ارفع فيديو': 'upload_video',
        'حمل فيديو': 'upload_video',
        'اعرض ثلاثي الأبعاد': 'show_3d',
        'افتح العرض ثلاثي الأبعاد': 'show_3d',
        'ولد ملخص': 'generate_summary',
        'اعمل ملخص': 'generate_summary',
        'أغلق': 'close_display',
        'إغلاق': 'close_display',
        'توقف عن الكلام': 'stop_conversation',
        'أوقف المحادثة': 'stop_conversation'
    };

    for (const [phrase, command] of Object.entries(commands)) {
        if (lowerTranscript.includes(phrase)) {
            return { isCommand: true, command: command };
        }
    }

    return { isCommand: false, command: null };
}

// تنفيذ أمر صوتي
async function executeVoiceCommand(command, originalText) {
    switch (command) {
        case 'screen_share':
            startScreenShare();
            return 'حسناً، سأبدأ مشاركة الشاشة الآن';

        case 'upload_video':
            uploadFile();
            return 'حسناً، يمكنك الآن اختيار الفيديو الذي تريد رفعه';

        case 'show_3d':
            show3DView();
            return 'ممتاز! سأعرض لك النموذج ثلاثي الأبعاد الآن';

        case 'generate_summary':
            generateSummary();
            return 'سأقوم بإنشاء ملخص لمحادثتنا الآن';

        case 'close_display':
            const displayArea = document.getElementById('displayArea');
            if (displayArea) {
                displayArea.style.display = 'none';
            }
            return 'تم إغلاق منطقة العرض';

        case 'stop_conversation':
            stopVoiceConversation();
            return 'حسناً، سأتوقف عن المحادثة الصوتية';

        default:
            return 'لم أفهم الأمر. يمكنك إعادة المحاولة';
    }
}

// بناء prompt مع السياق
function buildContextualPrompt(userInput) {
    let prompt = `أنت مساعد تقني ذكي تتحدث بطبيعية مع المستخدم. `;

    if (conversationContext) {
        prompt += `سياق المحادثة السابقة:\n${conversationContext}\n\n`;
    }

    prompt += `المستخدم يقول الآن: "${userInput}"\n\n`;
    prompt += `رد عليه بطريقة طبيعية ومفيدة، واجعل ردك مناسباً للمحادثة الصوتية (ليس طويلاً جداً).`;

    return prompt;
}

// تسجيل الصوت العادي (للتوافق)
function startVoiceRecording() {
    if (isInConversation) {
        // إذا كنا في وضع المحادثة، أوقفها
        stopVoiceConversation();
    } else {
        // ابدأ المحادثة التفاعلية
        startVoiceConversation();
    }
}

function stopVoiceRecording() {
    if (isInConversation) {
        stopVoiceConversation();
    } else {
        isListening = false;
        updateVoiceButton(false);
        console.log('⏹️ توقف التسجيل');
    }
}

function updateVoiceButton(active) {
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn) {
        if (active) {
            voiceBtn.classList.add('recording');
            voiceBtn.title = isInConversation ? 'جاري المحادثة... انقر للإيقاف' : 'جاري التسجيل...';
        } else {
            voiceBtn.classList.remove('recording');
            voiceBtn.title = 'ابدأ المحادثة الصوتية';
        }
    }
}

// مؤشر الاستماع
function updateListeningIndicator(listening) {
    const indicator = document.getElementById('listeningIndicator') || createListeningIndicator();

    if (listening) {
        indicator.style.display = 'block';
        indicator.innerHTML = '🎤 أستمع إليك...';
        indicator.className = 'listening-indicator active';
    } else {
        indicator.style.display = 'none';
        indicator.className = 'listening-indicator';
    }
}

function createListeningIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'listeningIndicator';
    indicator.className = 'listening-indicator';
    indicator.style.position = 'fixed';
    indicator.style.top = '20px';
    indicator.style.right = '20px';
    indicator.style.background = 'rgba(76, 175, 80, 0.9)';
    indicator.style.color = 'white';
    indicator.style.padding = '10px 15px';
    indicator.style.borderRadius = '25px';
    indicator.style.fontSize = '14px';
    indicator.style.fontWeight = 'bold';
    indicator.style.zIndex = '10000';
    indicator.style.display = 'none';
    indicator.style.animation = 'pulse 1.5s infinite';

    document.body.appendChild(indicator);
    return indicator;
}

// عرض النص المؤقت
function updateInterimText(text) {
    if (!text.trim()) return;

    let interimDiv = document.getElementById('interimText');
    if (!interimDiv) {
        interimDiv = document.createElement('div');
        interimDiv.id = 'interimText';
        interimDiv.style.position = 'fixed';
        interimDiv.style.bottom = '20px';
        interimDiv.style.left = '20px';
        interimDiv.style.right = '20px';
        interimDiv.style.background = 'rgba(33, 150, 243, 0.9)';
        interimDiv.style.color = 'white';
        interimDiv.style.padding = '10px';
        interimDiv.style.borderRadius = '8px';
        interimDiv.style.fontSize = '16px';
        interimDiv.style.zIndex = '10000';
        interimDiv.style.textAlign = 'center';

        document.body.appendChild(interimDiv);
    }

    interimDiv.textContent = `🗣️ "${text}"`;
    interimDiv.style.display = 'block';

    // إخفاء بعد ثانيتين
    clearTimeout(interimDiv.hideTimeout);
    interimDiv.hideTimeout = setTimeout(() => {
        interimDiv.style.display = 'none';
    }, 2000);
}

// مؤشر التفكير
function showThinkingIndicator() {
    let thinkingDiv = document.getElementById('thinkingIndicator');
    if (!thinkingDiv) {
        thinkingDiv = document.createElement('div');
        thinkingDiv.id = 'thinkingIndicator';
        thinkingDiv.style.position = 'fixed';
        thinkingDiv.style.top = '50%';
        thinkingDiv.style.left = '50%';
        thinkingDiv.style.transform = 'translate(-50%, -50%)';
        thinkingDiv.style.background = 'rgba(0, 0, 0, 0.8)';
        thinkingDiv.style.color = 'white';
        thinkingDiv.style.padding = '20px';
        thinkingDiv.style.borderRadius = '10px';
        thinkingDiv.style.fontSize = '18px';
        thinkingDiv.style.zIndex = '10001';
        thinkingDiv.style.textAlign = 'center';

        document.body.appendChild(thinkingDiv);
    }

    thinkingDiv.innerHTML = '🤔 أفكر في ردي...';
    thinkingDiv.style.display = 'block';
}

function hideThinkingIndicator() {
    const thinkingDiv = document.getElementById('thinkingIndicator');
    if (thinkingDiv) {
        thinkingDiv.style.display = 'none';
    }
}

// ===== وظائف الأدوات =====

// مشاركة الشاشة مع المساعد الصوتي المرئي
async function startScreenShare() {
    if (!navigator.mediaDevices?.getDisplayMedia) {
        addMessage('assistant', 'المتصفح لا يدعم مشاركة الشاشة');
        speakText('عذراً، المتصفح لا يدعم مشاركة الشاشة');
        return;
    }

    try {
        addMessage('assistant', 'جاري تفعيل مشاركة الشاشة مع المساعد الصوتي المرئي...');
        speakText('جاري تفعيل مشاركة الشاشة مع المساعد الصوتي المرئي');

        const stream = await navigator.mediaDevices.getDisplayMedia({
            video: {
                mediaSource: 'screen',
                width: { ideal: 1920 },
                height: { ideal: 1080 },
                frameRate: { ideal: 30 }
            },
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                sampleRate: 44100
            }
        });

        // إنشاء المساعد الصوتي المرئي
        const visualAssistant = createVisualAssistant(stream);
        displayInArea('المساعد الصوتي المرئي', visualAssistant);

        addMessage('assistant', 'تم تفعيل المساعد الصوتي المرئي! أستطيع الآن رؤية شاشتك والتحدث معك حول ما أراه.');

        const welcomeMessage = 'مرحباً! تم تفعيل المساعد الصوتي المرئي بنجاح! أستطيع الآن رؤية شاشتك والتحدث معك حول ما أراه. يمكنك أن تسألني عن أي شيء تراه على الشاشة، أو تطلب مني تحليل ما يحدث، أو حتى أن نتحدث بشكل طبيعي حول عملك';

        speakText(welcomeMessage, {
            onEnd: () => {
                // بدء المحادثة الصوتية التلقائية مع مشاركة الشاشة
                if (!isInConversation) {
                    isInConversation = true;
                    speechSettings.conversationMode = true;
                    setTimeout(() => {
                        speakText('الآن يمكنك التحدث معي بطبيعية. ما الذي تعمل عليه؟', {
                            onEnd: () => startContinuousListening()
                        });
                    }, 2000);
                }
            }
        });

        // بدء التحليل المرئي والصوتي
        startVisualAnalysis();

    } catch (error) {
        console.error('خطأ في مشاركة الشاشة:', error);
        let errorMessage = 'فشل في تفعيل المساعد الصوتي المرئي';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن لمشاركة الشاشة والصوت';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على شاشة أو صوت للمشاركة';
        }

        addMessage('assistant', errorMessage);
        speakText(errorMessage);
    }
}

// رفع الملفات
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    if (!fileInput) return;

    fileInput.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
            handleFile(file);
        }
    };
    fileInput.click();
}

function handleFile(file) {
    addMessage('assistant', `تم تحميل الملف: ${file.name}`);

    if (file.type.startsWith('video/')) {
        const video = document.createElement('video');
        video.src = URL.createObjectURL(file);
        video.controls = true;
        video.style.width = '100%';
        displayInArea('عرض الفيديو', video);
    } else if (file.type.startsWith('image/')) {
        const img = document.createElement('img');
        img.src = URL.createObjectURL(file);
        img.style.maxWidth = '100%';
        displayInArea('عرض الصورة', img);
    }
}

// العرض ثلاثي الأبعاد
function show3DView() {
    if (!window.THREE) {
        addMessage('assistant', 'مكتبة Three.js غير متاحة');
        return;
    }

    addMessage('assistant', 'جاري تحميل العرض ثلاثي الأبعاد...');

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 400/300, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(400, 300);

    // إنشاء مكعب
    const geometry = new THREE.BoxGeometry();
    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    const cube = new THREE.Mesh(geometry, material);
    scene.add(cube);

    camera.position.z = 5;

    function animate() {
        requestAnimationFrame(animate);
        cube.rotation.x += 0.01;
        cube.rotation.y += 0.01;
        renderer.render(scene, camera);
    }

    animate();
    displayInArea('العرض ثلاثي الأبعاد', renderer.domElement);
    addMessage('assistant', 'تم تفعيل العرض ثلاثي الأبعاد!');
}

// توليد ملخص
function generateSummary() {
    if (window.conversationHistory.length === 0) {
        addMessage('assistant', 'لا توجد محادثة لتلخيصها');
        return;
    }

    const userMessages = window.conversationHistory.filter(msg => msg.role === 'user');
    const summary = `
📊 ملخص المحادثة:

📈 الإحصائيات:
• إجمالي الرسائل: ${window.conversationHistory.length}
• رسائل المستخدم: ${userMessages.length}

❓ آخر الأسئلة:
${userMessages.slice(-3).map((msg, i) => `${i+1}. ${msg.content}`).join('\n')}
    `;

    const summaryDiv = document.createElement('div');
    summaryDiv.style.padding = '20px';
    summaryDiv.style.whiteSpace = 'pre-line';
    summaryDiv.textContent = summary;

    displayInArea('ملخص المحادثة', summaryDiv);
    addMessage('assistant', 'تم إنشاء ملخص المحادثة!');
}

// عرض في منطقة العرض
function displayInArea(title, element) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    if (!displayArea || !displayContent || !displayTitle) return;

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.marginTop = '15px';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
    };

    displayTitle.textContent = title;
    displayContent.innerHTML = '';
    displayContent.appendChild(element);
    displayContent.appendChild(closeBtn);
    displayArea.style.display = 'flex';
}

function createVideoElement(stream) {
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';

    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف';
    stopBtn.className = 'tool-btn';
    stopBtn.style.marginTop = '10px';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.getElementById('displayArea').style.display = 'none';
    };

    const container = document.createElement('div');
    container.appendChild(video);
    container.appendChild(stopBtn);

    return container;
}

// ===== المساعد الصوتي المرئي =====

// إنشاء واجهة المساعد الصوتي المرئي
function createVisualAssistant(stream) {
    const container = document.createElement('div');
    container.style.position = 'relative';

    // عنصر الفيديو الرئيسي
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';
    video.style.border = '3px solid #4CAF50';
    video.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.5)';

    // مؤشر الحالة المباشرة
    const liveIndicator = document.createElement('div');
    liveIndicator.innerHTML = '🔴 مباشر - المساعد يراقب';
    liveIndicator.style.position = 'absolute';
    liveIndicator.style.top = '10px';
    liveIndicator.style.left = '10px';
    liveIndicator.style.background = 'rgba(255, 0, 0, 0.8)';
    liveIndicator.style.color = 'white';
    liveIndicator.style.padding = '5px 10px';
    liveIndicator.style.borderRadius = '15px';
    liveIndicator.style.fontSize = '12px';
    liveIndicator.style.fontWeight = 'bold';
    liveIndicator.style.animation = 'pulse 2s infinite';

    // منطقة التحليل المرئي
    const analysisOverlay = document.createElement('div');
    analysisOverlay.id = 'analysisOverlay';
    analysisOverlay.style.position = 'absolute';
    analysisOverlay.style.bottom = '10px';
    analysisOverlay.style.left = '10px';
    analysisOverlay.style.right = '10px';
    analysisOverlay.style.background = 'rgba(0, 0, 0, 0.7)';
    analysisOverlay.style.color = 'white';
    analysisOverlay.style.padding = '10px';
    analysisOverlay.style.borderRadius = '8px';
    analysisOverlay.style.fontSize = '14px';
    analysisOverlay.style.maxHeight = '100px';
    analysisOverlay.style.overflowY = 'auto';
    analysisOverlay.innerHTML = '🤖 المساعد جاهز للتحليل المرئي...';

    // أزرار التحكم
    const controls = document.createElement('div');
    controls.style.marginTop = '15px';
    controls.style.display = 'flex';
    controls.style.gap = '10px';
    controls.style.justifyContent = 'center';
    controls.style.flexWrap = 'wrap';

    // زر تحليل فوري
    const analyzeBtn = document.createElement('button');
    analyzeBtn.innerHTML = '<i class="fas fa-eye"></i> تحليل فوري';
    analyzeBtn.className = 'tool-btn';
    analyzeBtn.style.background = '#2196F3';
    analyzeBtn.onclick = () => performInstantAnalysis();

    // زر التعليق الصوتي
    const commentBtn = document.createElement('button');
    commentBtn.innerHTML = '<i class="fas fa-microphone"></i> تعليق صوتي';
    commentBtn.className = 'tool-btn';
    commentBtn.style.background = '#FF9800';
    commentBtn.onclick = () => startVoiceCommentary();

    // زر إيقاف/تشغيل الصوت
    const muteBtn = document.createElement('button');
    muteBtn.innerHTML = '<i class="fas fa-volume-up"></i> إيقاف الصوت';
    muteBtn.className = 'tool-btn';
    muteBtn.style.background = '#9C27B0';
    muteBtn.onclick = () => toggleAssistantVoice(muteBtn);

    // زر إيقاف المشاركة
    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
    stopBtn.className = 'tool-btn';
    stopBtn.style.background = '#f44336';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.getElementById('displayArea').style.display = 'none';
        addMessage('assistant', 'تم إيقاف المساعد الصوتي المرئي');
        speakText('تم إيقاف المساعد الصوتي المرئي. شكراً لاستخدام الخدمة');
        stopVisualAnalysis();
    };

    controls.appendChild(analyzeBtn);
    controls.appendChild(commentBtn);
    controls.appendChild(muteBtn);
    controls.appendChild(stopBtn);

    // تجميع العناصر
    container.appendChild(video);
    container.appendChild(liveIndicator);
    container.appendChild(analysisOverlay);
    container.appendChild(controls);

    return container;
}

// متغيرات المساعد المرئي
let visualAnalysisInterval = null;
let assistantVoiceEnabled = true;
let analysisCount = 0;

// بدء التحليل المرئي المستمر
function startVisualAnalysis() {
    console.log('🔍 بدء التحليل المرئي المستمر');

    // تحليل دوري كل 15 ثانية (أقل تكراراً لتجنب الإزعاج)
    visualAnalysisInterval = setInterval(() => {
        if (assistantVoiceEnabled && isInConversation) {
            performPeriodicAnalysis();
        }
    }, 15000);

    // تحليل فوري بعد 5 ثوان من البداية
    setTimeout(() => {
        if (assistantVoiceEnabled && isInConversation) {
            performInstantAnalysis();
        }
    }, 5000);
}

// إيقاف التحليل المرئي
function stopVisualAnalysis() {
    if (visualAnalysisInterval) {
        clearInterval(visualAnalysisInterval);
        visualAnalysisInterval = null;
    }
    analysisCount = 0;
    console.log('⏹️ تم إيقاف التحليل المرئي');
}

// تحليل فوري للشاشة
function performInstantAnalysis() {
    analysisCount++;
    const overlay = document.getElementById('analysisOverlay');

    if (overlay) {
        overlay.innerHTML = '🔍 جاري التحليل الفوري...';
    }

    // محاكاة تحليل ذكي للشاشة
    setTimeout(() => {
        const analysis = generateScreenAnalysis();

        if (overlay) {
            overlay.innerHTML = `🤖 التحليل #${analysisCount}: ${analysis.text}`;
        }

        addMessage('assistant', `تحليل مرئي: ${analysis.text}`);

        if (assistantVoiceEnabled) {
            speakText(analysis.voice);
        }
    }, 2000);
}

// تحليل دوري
function performPeriodicAnalysis() {
    const analysis = generatePeriodicAnalysis();
    const overlay = document.getElementById('analysisOverlay');

    if (overlay) {
        overlay.innerHTML = `🔄 تحليل دوري: ${analysis.text}`;
    }

    if (assistantVoiceEnabled && Math.random() > 0.3) { // 70% احتمال للتعليق الصوتي
        speakText(analysis.voice);
    }
}

// توليد تحليل ذكي للشاشة
function generateScreenAnalysis() {
    const currentTime = new Date().getHours();
    const timeOfDay = currentTime < 12 ? 'صباح' : currentTime < 18 ? 'بعد الظهر' : 'مساء';

    const contextualAnalyses = [
        {
            text: `أرى أنك تعمل في فترة ${timeOfDay}. الشاشة تظهر نشاطاً تقنياً`,
            voice: `أراك تعمل بجد في هذا ${timeOfDay}. ما الذي تعمل عليه؟ يمكنني مساعدتك`
        },
        {
            text: 'أرى واجهة المساعد التقني. تطبيق رائع للعمل التقني',
            voice: 'أرى أنك تستخدم المساعد التقني الذكي. هذا اختيار ممتاز! كيف يمكنني مساعدتك اليوم؟'
        },
        {
            text: 'الشاشة تحتوي على عناصر تفاعلية متعددة',
            voice: 'أرى عناصر تفاعلية كثيرة على الشاشة. هل تريد مني شرح أي منها أو مساعدتك في استخدامها؟'
        },
        {
            text: 'يبدو أنك في بيئة تطوير أو عمل تقني',
            voice: 'أرى أنك في بيئة عمل تقنية. أنا خبير في البرمجة والتقنية، يمكنني مساعدتك في أي شيء'
        },
        {
            text: 'النشاط يشير إلى عمل إنتاجي ومركز',
            voice: 'أرى أنك تعمل بتركيز. هذا رائع! إذا احتجت استراحة أو مساعدة، أنا هنا'
        },
        {
            text: 'الشاشة تظهر تنظيماً جيداً للعمل',
            voice: 'أعجبني تنظيم عملك على الشاشة. هل تريد مني اقتراح طرق لتحسين الإنتاجية أكثر؟'
        }
    ];

    return contextualAnalyses[Math.floor(Math.random() * contextualAnalyses.length)];
}

// توليد تحليل دوري
function generatePeriodicAnalysis() {
    const analyses = [
        {
            text: 'مراقبة مستمرة... كل شيء يبدو طبيعياً',
            voice: 'أراقب شاشتك باستمرار. كل شيء يبدو جيداً'
        },
        {
            text: 'تحديث دوري: النشاط مستمر على الشاشة',
            voice: 'أرى نشاطاً مستمراً. هل تحتاج مساعدة في شيء محدد؟'
        },
        {
            text: 'فحص دوري: الشاشة تعمل بشكل طبيعي',
            voice: 'فحص دوري مكتمل. أستطيع مساعدتك في أي وقت'
        }
    ];

    return analyses[Math.floor(Math.random() * analyses.length)];
}

// بدء التعليق الصوتي المباشر
function startVoiceCommentary() {
    addMessage('assistant', 'بدء التعليق الصوتي المباشر...');
    speakText('سأبدأ الآن بالتعليق الصوتي المباشر على ما أراه على شاشتك');

    // تعليق فوري
    setTimeout(() => {
        const commentary = generateVoiceCommentary();
        addMessage('assistant', `تعليق مباشر: ${commentary}`);
        speakText(commentary);
    }, 2000);
}

// توليد تعليق صوتي تفاعلي
function generateVoiceCommentary() {
    const interactiveCommentaries = [
        'أرى أنك تستخدم المساعد التقني الذكي. هذا اختيار ممتاز! هل تريد مني شرح أي من الميزات المتاحة؟',
        'الواجهة تبدو منظمة وسهلة الاستخدام. يمكنني مساعدتك في استكشاف جميع الأدوات المتاحة',
        'أرى إمكانيات رائعة في هذا التطبيق. هل تريد مني أن أوضح لك كيفية استخدام أي منها؟',
        'بيئة العمل تبدو احترافية ومتقدمة. ما نوع المشروع الذي تعمل عليه؟ يمكنني تقديم نصائح مفيدة',
        'أرى أنك تعمل بتركيز وكفاءة. هل تحتاج مساعدة في تحسين سير العمل أو حل أي مشكلة تقنية؟',
        'الشاشة تظهر تنوعاً في الأدوات المتاحة. أي منها تستخدم أكثر؟ يمكنني إعطاؤك نصائح لاستخدامها بفعالية أكبر',
        'أرى أنك تتنقل بين عدة عناصر. هل تريد مني تنظيم سير العمل أو اقتراح طرق أفضل للتنقل؟'
    ];

    return interactiveCommentaries[Math.floor(Math.random() * interactiveCommentaries.length)];
}

// تبديل صوت المساعد
function toggleAssistantVoice(button) {
    assistantVoiceEnabled = !assistantVoiceEnabled;

    if (assistantVoiceEnabled) {
        button.innerHTML = '<i class="fas fa-volume-up"></i> إيقاف الصوت';
        button.style.background = '#9C27B0';
        addMessage('assistant', 'تم تفعيل الصوت');
        speakText('تم تفعيل صوت المساعد المرئي');
    } else {
        button.innerHTML = '<i class="fas fa-volume-mute"></i> تفعيل الصوت';
        button.style.background = '#757575';
        addMessage('assistant', 'تم إيقاف الصوت');
    }
}

// إعدادات الصوت
function openVoiceSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// ===== ربط الأحداث =====

function initializeApp() {
    console.log('🚀 تهيئة التطبيق...');

    // ربط الأحداث
    bindEvents();

    // فحص الاتصال
    checkConnection();

    // رسالة ترحيب
    setTimeout(() => {
        addMessage('assistant', 'مرحباً! أنا مساعدك التقني الذكي. يمكنني مساعدتك في البرمجة والتقنية والمحادثة الصوتية. انقر على زر الميكروفون لبدء محادثة صوتية تفاعلية!');

        if (speechSettings.enabled) {
            speakText('مرحباً بك! أنا مساعدك التقني الذكي. يمكنني مساعدتك في البرمجة والتقنية. انقر على زر الميكروفون الأحمر لبدء محادثة صوتية تفاعلية معي');
        }
    }, 2000);

    console.log('✅ تم تهيئة التطبيق');
}

function bindEvents() {
    // زر الإرسال
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        sendBtn.onclick = sendMessage;
    }

    // حقل الإدخال
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }

    // زر التسجيل الصوتي
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn) {
        voiceBtn.onclick = startVoiceRecording;
    }

    // أزرار الأدوات
    const screenShareBtn = document.getElementById('screenShareBtn');
    if (screenShareBtn) {
        screenShareBtn.onclick = startScreenShare;
    }

    const videoUploadBtn = document.getElementById('videoUploadBtn');
    if (videoUploadBtn) {
        videoUploadBtn.onclick = uploadFile;
    }

    const videoAnalyzeBtn = document.getElementById('videoAnalyzeBtn');
    if (videoAnalyzeBtn) {
        videoAnalyzeBtn.onclick = uploadFile;
    }

    const ar3dBtn = document.getElementById('ar3dBtn');
    if (ar3dBtn) {
        ar3dBtn.onclick = show3DView;
    }

    const summaryBtn = document.getElementById('summaryBtn');
    if (summaryBtn) {
        summaryBtn.onclick = generateSummary;
    }

    const voiceSettingsBtn = document.getElementById('voiceBtn');
    if (voiceSettingsBtn) {
        voiceSettingsBtn.onclick = openVoiceSettings;
    }

    // أزرار الإغلاق
    const closeDisplay = document.getElementById('closeDisplay');
    if (closeDisplay) {
        closeDisplay.onclick = () => {
            document.getElementById('displayArea').style.display = 'none';
        };
    }

    const closeSettingsBtn = document.getElementById('closeSettings');
    if (closeSettingsBtn) {
        closeSettingsBtn.onclick = closeSettings;
    }

    console.log('✅ تم ربط جميع الأحداث');
}

async function checkConnection() {
    try {
        const isConnected = await technicalAssistant.checkConnection();
        updateConnectionStatus(isConnected);
    } catch (error) {
        updateConnectionStatus(false);
    }
}

function updateConnectionStatus(isConnected) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-indicator span:last-child');

    if (statusDot && statusText) {
        if (isConnected) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }
}

// تصدير الوظائف
window.technicalAssistant = technicalAssistant;
window.sendMessage = sendMessage;
window.startVoiceRecording = startVoiceRecording;
window.startScreenShare = startScreenShare;
window.uploadFile = uploadFile;
window.show3DView = show3DView;
window.generateSummary = generateSummary;
window.openVoiceSettings = openVoiceSettings;
window.closeSettings = closeSettings;
window.initializeApp = initializeApp;
window.addMessage = addMessage;
window.speakText = speakText;
