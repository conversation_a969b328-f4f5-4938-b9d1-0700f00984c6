const MODEL = "deepseek-coder-6.7b-instruct";
const API_URL = "http://localhost:1234/v1/chat/completions";

async function askAssistant(userPrompt, systemPrompt = "") {
    try {
        const body = {
            model: MODEL,
            messages: [
                {
                    role: "system",
                    content: systemPrompt || "أنت مساعد تقني ذكي جدًا، قادر على تنفيذ أي أمر يُطلب منك بدقة. تستخدم أسلوبًا احترافيًا في الرد وتفهم كل ما يطلبه المستخدم، سواء كان طلب برمجة، شرح فيديو، توليد كائن 3D، أو أمر صوتي. اعتمد على فهمك الكامل للسياق وقدم نتائج فورية دقيقة، بأسلوب مختصر ومهني."
                },
                { role: "user", content: userPrompt }
            ],
            temperature: 0.7,
            stream: false
        };

        const res = await fetch(API_URL, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(body)
        });

        if (!res.ok) throw new Error("فشل الاتصال بـ LM Studio.");

        const data = await res.json();
        return data.choices[0].message.content.trim();

    } catch (err) {
        console.error("Assistant Error:", err);
        return "⚠️ حدث خطأ أثناء التواصل مع النموذج.";
    }
}

// عرض الرسالة في واجهة المستخدم
function appendMessage(role, text) {
    const container = document.getElementById("chatContainer");
    const msgDiv = document.createElement("div");
    msgDiv.className = role === "user" ? "user-message" : "assistant-message";
    msgDiv.innerHTML = `<div class="avatar"><i class="fas ${role === "user" ? "fa-user" : "fa-robot"}"></i></div>
                        <div class="message-content"><p>${text}</p></div>`;
    container.appendChild(msgDiv);
    container.scrollTop = container.scrollHeight;
}

// الرد وتشغيل الصوت
async function handleMessageSend(text) {
    if (!text.trim()) return;
    appendMessage("user", text);
    document.getElementById("messageInput").value = "";

    const response = await askAssistant(text);
    appendMessage("assistant", response);

    // تشغيل الرد بالصوت إن وُجد
    if (window.responsiveVoice && responsiveVoice.voiceSupport()) {
        const voiceLang = document.getElementById("voiceLanguage").value || "ar-SA";
        const rate = parseFloat(document.getElementById("speechRate").value || "1");
        const vol = parseFloat(document.getElementById("speechVolume").value || "0.8");
        responsiveVoice.speak(response, voiceLang, { rate: rate, volume: vol });
    }
}

// ربط الأحداث
document.addEventListener("DOMContentLoaded", () => {
    document.getElementById("sendBtn").addEventListener("click", () => {
        handleMessageSend(document.getElementById("messageInput").value);
    });

    document.getElementById("messageInput").addEventListener("keypress", (e) => {
        if (e.key === "Enter") handleMessageSend(e.target.value);
    });

    // زر المايكروفون (بشكل بسيط كبداية)
    document.getElementById("voiceRecordBtn").addEventListener("click", () => {
        alert("🎙️ جاري إضافة دعم التسجيل الصوتي... (الميزة تحت التطوير)");
    });
});
