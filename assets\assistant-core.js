// منطق المساعد الأساسي والذكاء الاصطناعي المحلي
// Core Assistant Logic and Local AI

class TechnicalAssistant {
    constructor() {
        this.MODEL = "deepseek-coder-6.7b-instruct";
        this.API_URL = "http://localhost:1234/v1/chat/completions";
        this.knowledgeBase = this.initializeKnowledgeBase();
        this.conversationHistory = [];
        this.userPreferences = {};
        this.currentContext = {};
    }

    // تهيئة قاعدة المعرفة التقنية
    initializeKnowledgeBase() {
        return {
            programming: {
                javascript: {
                    basics: 'JavaScript هي لغة برمجة ديناميكية تستخدم في تطوير الويب',
                    functions: 'الدوال في JavaScript تُعرف باستخدام function أو =>'
                },
                python: {
                    basics: 'Python لغة برمجة عالية المستوى سهلة التعلم',
                    syntax: 'Python يستخدم المسافات البادئة لتحديد الكتل'
                },
                html: {
                    basics: 'HTML هي لغة ترميز لإنشاء صفحات الويب',
                    tags: 'HTML يستخدم العلامات (tags) لتنظيم المحتوى'
                },
                css: {
                    basics: 'CSS تستخدم لتنسيق وتصميم صفحات الويب',
                    selectors: 'CSS تستخدم المحددات لاستهداف عناصر HTML'
                }
            },
            networking: {
                basics: 'الشبكات تربط الأجهزة لتبادل البيانات',
                protocols: 'البروتوكولات مثل HTTP وTCP/IP تنظم التواصل'
            },
            databases: {
                sql: 'SQL لغة استعلام قواعد البيانات العلائقية',
                nosql: 'قواعد البيانات غير العلائقية مثل MongoDB'
            },
            security: {
                basics: 'أمن المعلومات يحمي البيانات من التهديدات',
                encryption: 'التشفير يحول البيانات إلى شكل غير مقروء'
            }
        };
    }

    // الاتصال مع LM Studio
    async askAssistant(userPrompt, systemPrompt = "") {
        try {
            const body = {
                model: this.MODEL,
                messages: [
                    {
                        role: "system",
                        content: systemPrompt || "أنت مساعد تقني ذكي جدًا، قادر على تنفيذ أي أمر يُطلب منك بدقة. تستخدم أسلوبًا احترافيًا في الرد وتفهم كل ما يطلبه المستخدم، سواء كان طلب برمجة، شرح فيديو، توليد كائن 3D، أو أمر صوتي. اعتمد على فهمك الكامل للسياق وقدم نتائج فورية دقيقة، بأسلوب مختصر ومهني."
                    },
                    { role: "user", content: userPrompt }
                ],
                temperature: 0.7,
                stream: false
            };

            const res = await fetch(this.API_URL, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(body)
            });

            if (!res.ok) throw new Error("فشل الاتصال بـ LM Studio.");

            const data = await res.json();
            return data.choices[0].message.content.trim();

        } catch (err) {
            console.error("Assistant Error:", err);
            return "⚠️ حدث خطأ أثناء التواصل مع النموذج. تأكد من تشغيل LM Studio على المنفذ 1234.";
        }
    }

    // تحليل السؤال وتحديد النوع
    analyzeQuestion(question) {
        const lowerQuestion = question.toLowerCase();

        // تحديد الموضوع
        let topic = 'general';
        let subtopic = null;
        let questionType = 'explanation';

        // تحليل الموضوع
        if (this.containsKeywords(lowerQuestion, ['javascript', 'js', 'جافا سكريبت'])) {
            topic = 'programming';
            subtopic = 'javascript';
        } else if (this.containsKeywords(lowerQuestion, ['python', 'بايثون'])) {
            topic = 'programming';
            subtopic = 'python';
        } else if (this.containsKeywords(lowerQuestion, ['html', 'إتش تي إم إل'])) {
            topic = 'programming';
            subtopic = 'html';
        } else if (this.containsKeywords(lowerQuestion, ['css', 'سي إس إس'])) {
            topic = 'programming';
            subtopic = 'css';
        } else if (this.containsKeywords(lowerQuestion, ['شبكة', 'network', 'إنترنت'])) {
            topic = 'networking';
        } else if (this.containsKeywords(lowerQuestion, ['قاعدة بيانات', 'database', 'sql'])) {
            topic = 'databases';
        } else if (this.containsKeywords(lowerQuestion, ['أمان', 'security', 'تشفير'])) {
            topic = 'security';
        }

        // تحديد نوع السؤال
        if (this.containsKeywords(lowerQuestion, ['كيف', 'how', 'شلون'])) {
            questionType = 'howto';
        } else if (this.containsKeywords(lowerQuestion, ['ما هو', 'what', 'شنو'])) {
            questionType = 'definition';
        } else if (this.containsKeywords(lowerQuestion, ['مثال', 'example', 'كود'])) {
            questionType = 'example';
        } else if (this.containsKeywords(lowerQuestion, ['مشكلة', 'خطأ', 'error', 'bug'])) {
            questionType = 'troubleshooting';
        }

        return { topic, subtopic, questionType };
    }

    // فحص وجود كلمات مفتاحية
    containsKeywords(text, keywords) {
        return keywords.some(keyword => text.includes(keyword));
    }

    // الحصول على إجابة شاملة
    async getResponse(question) {
        try {
            // محاولة الحصول على رد من LM Studio أولاً
            const aiResponse = await this.askAssistant(question);

            // إذا كان الرد من AI صالحاً، استخدمه
            if (aiResponse && !aiResponse.includes('⚠️ حدث خطأ')) {
                this.updateContext(question, aiResponse);
                return aiResponse;
            }

            // إذا فشل AI، استخدم المعرفة المحلية
            const analysis = this.analyzeQuestion(question);
            const localResponse = this.generateLocalResponse(question, analysis);
            this.updateContext(question, localResponse);
            return localResponse;

        } catch (error) {
            console.error('خطأ في getResponse:', error);
            return 'عذراً، حدث خطأ في معالجة سؤالك. يرجى المحاولة مرة أخرى.';
        }
    }

    // توليد رد محلي
    generateLocalResponse(question, analysis) {
        const { topic, subtopic, questionType } = analysis;

        // الحصول على المعلومات من قاعدة المعرفة
        const knowledge = this.getKnowledge(topic, subtopic);

        if (knowledge) {
            return `بناءً على معرفتي المحلية:\n\n${knowledge.basics || knowledge}\n\nهل تريد المزيد من التفاصيل؟`;
        }

        return `أفهم سؤالك حول "${question}". دعني أساعدك بأفضل ما أستطيع بناءً على معرفتي التقنية.`;
    }

    // الحصول على المعرفة من قاعدة البيانات
    getKnowledge(topic, subtopic) {
        if (subtopic && this.knowledgeBase[topic] && this.knowledgeBase[topic][subtopic]) {
            return this.knowledgeBase[topic][subtopic];
        } else if (this.knowledgeBase[topic]) {
            return this.knowledgeBase[topic];
        }
        return null;
    }

    // حفظ السياق للمحادثة
    updateContext(question, response) {
        this.currentContext = {
            lastQuestion: question,
            lastResponse: response,
            timestamp: new Date().toISOString()
        };

        this.conversationHistory.push({
            question: question,
            response: response,
            timestamp: new Date().toISOString()
        });
    }
}

// إنشاء مثيل المساعد التقني
const technicalAssistant = new TechnicalAssistant();

// تصدير المساعد للاستخدام العام
window.technicalAssistant = technicalAssistant;
