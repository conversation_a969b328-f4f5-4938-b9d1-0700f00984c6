/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

/* الشريط العلوي */
.top-bar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-section i {
    font-size: 2rem;
    color: #667eea;
    animation: pulse 2s infinite;
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: blink 1.5s infinite;
}

.status-dot.online {
    background: #4CAF50;
}

.status-dot.offline {
    background: #ff4757;
}

/* تسجيل صوتي */
.recording {
    background: #ff4757 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* المساعد الصوتي المرئي */
.visual-assistant-container {
    position: relative;
    border: 3px solid #4CAF50;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.3);
}

.live-indicator {
    animation: pulse 2s infinite;
    z-index: 10;
}

.analysis-overlay {
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.analysis-overlay:hover {
    background: rgba(0, 0, 0, 0.9) !important;
}

/* مؤشرات المحادثة الصوتية */
.listening-indicator {
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
}

.listening-indicator.active {
    animation: pulse 1.5s infinite;
    background: rgba(76, 175, 80, 1) !important;
}

/* النص المؤقت */
#interimText {
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* مؤشر التفكير */
#thinkingIndicator {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
}

/* تحسين زر التسجيل */
.recording {
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    animation: pulse 1s infinite, glow 2s infinite alternate;
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.5);
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(255, 71, 87, 0.5); }
    to { box-shadow: 0 0 30px rgba(255, 71, 87, 0.8); }
}

/* مؤشر الكتابة المتقدم */
.advanced-typing {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.thinking-text {
    font-size: 14px;
    color: #666;
    font-style: italic;
    animation: fadeInOut 2s infinite;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* تنسيق رسائل المساعد المحسن */
.message-content h3.message-heading {
    color: #2196F3;
    margin: 10px 0 8px 0;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 2px solid #2196F3;
    padding-bottom: 4px;
}

.message-content .inline-code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #d63384;
    border: 1px solid #e0e0e0;
}

.code-block {
    margin: 15px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.code-header {
    background: #2d3748;
    color: white;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.code-lang {
    font-weight: bold;
    text-transform: uppercase;
}

.copy-code-btn {
    background: #4a5568;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-code-btn:hover {
    background: #2d3748;
}

.code-block pre {
    background: #1a202c;
    color: #e2e8f0;
    padding: 15px;
    margin: 0;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.emoji-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.emoji-list-item {
    padding: 5px 0;
    border-left: 3px solid #4CAF50;
    padding-left: 10px;
    margin: 5px 0;
    background: rgba(76, 175, 80, 0.05);
    border-radius: 0 4px 4px 0;
}

.message-link {
    color: #2196F3;
    text-decoration: none;
    border-bottom: 1px dotted #2196F3;
    transition: all 0.3s;
}

.message-link:hover {
    color: #1976D2;
    border-bottom: 1px solid #1976D2;
}

/* تحسين عرض الرسائل */
.message.assistant .message-content {
    line-height: 1.6;
}

.message.assistant .message-content p {
    margin: 8px 0;
}

.message.assistant .message-content strong {
    color: #1976D2;
    font-weight: 600;
}

.message.assistant .message-content em {
    color: #666;
    font-style: italic;
}

/* الحاوية الرئيسية */
.main-container {
    display: flex;
    height: calc(100vh - 80px);
    gap: 20px;
    padding: 20px;
}

/* الشريط الجانبي */
.sidebar {
    width: 250px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tool-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.1rem;
    text-align: center;
}

.tool-btn {
    width: 100%;
    padding: 15px;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.tool-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.tool-btn i {
    font-size: 1.2rem;
}

/* منطقة المحادثة */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chat-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.welcome-message {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    animation: fadeInUp 0.6s ease;
}

.avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.message-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    border-top-right-radius: 5px;
    max-width: 80%;
}

.message-content h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.message-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 8px;
}

/* منطقة الإدخال */
.input-area {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.input-container {
    display: flex;
    gap: 10px;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.voice-record-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.voice-record-btn:hover {
    transform: scale(1.1);
}

.voice-record-btn.recording {
    background: #ff4757;
    animation: pulse 1s infinite;
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    padding: 15px 20px;
    font-size: 1rem;
    background: transparent;
}

.send-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.send-btn:hover {
    transform: scale(1.1);
}

/* منطقة السحب والإفلات */
.file-drop-zone {
    margin-top: 15px;
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.5);
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.file-drop-zone i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
}

/* منطقة العرض */
.display-area {
    width: 400px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.display-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-display {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.close-display:hover {
    color: #ff4757;
}

.display-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.setting-group select,
.setting-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

/* الرسائل */
.message {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    animation: fadeInUp 0.4s ease;
}

.message.user {
    flex-direction: row-reverse;
}

.message.user .message-content {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-top-left-radius: 5px;
    border-top-right-radius: 15px;
}

/* الحركات */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* مؤشر الكتابة */
.typing-indicator .typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
        padding: 10px;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .display-area {
        width: 100%;
    }

    .tool-btn {
        padding: 10px;
        font-size: 0.8rem;
    }
}
