// وحدة مشاركة الشاشة
// Screen Sharing Module

class ScreenShareManager {
    constructor() {
        this.mediaStream = null;
        this.isSharing = false;
        this.recordedChunks = [];
        this.mediaRecorder = null;
    }

    // بدء مشاركة الشاشة
    async startScreenShare() {
        try {
            console.log('🖥️ بدء مشاركة الشاشة...');
            
            // طلب إذن مشاركة الشاشة
            this.mediaStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'screen',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    frameRate: { ideal: 30 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });

            this.isSharing = true;
            console.log('✅ تم بدء مشاركة الشاشة بنجاح');

            // عرض الشاشة المشاركة
            this.displaySharedScreen();

            // إعداد التسجيل
            this.setupRecording();

            // مراقبة إيقاف المشاركة
            this.mediaStream.getVideoTracks()[0].addEventListener('ended', () => {
                this.stopScreenShare();
            });

            // إضافة رسالة للمحادثة
            addMessageToChat('assistant', 'تم بدء مشاركة الشاشة بنجاح! يمكنك الآن عرض شاشتك وتسجيلها.');

            return true;

        } catch (error) {
            console.error('❌ خطأ في مشاركة الشاشة:', error);
            this.handleScreenShareError(error);
            return false;
        }
    }

    // عرض الشاشة المشاركة
    displaySharedScreen() {
        const displayArea = document.getElementById('displayArea');
        const displayContent = document.getElementById('displayContent');
        const displayTitle = document.getElementById('displayTitle');

        // إنشاء عنصر الفيديو
        const videoElement = document.createElement('video');
        videoElement.id = 'sharedScreenVideo';
        videoElement.autoplay = true;
        videoElement.muted = true;
        videoElement.style.width = '100%';
        videoElement.style.height = 'auto';
        videoElement.style.borderRadius = '8px';
        videoElement.srcObject = this.mediaStream;

        // إنشاء أزرار التحكم
        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'screen-share-controls';
        controlsDiv.style.marginTop = '15px';
        controlsDiv.style.display = 'flex';
        controlsDiv.style.gap = '10px';
        controlsDiv.style.justifyContent = 'center';

        // زر التسجيل
        const recordBtn = document.createElement('button');
        recordBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> بدء التسجيل';
        recordBtn.className = 'tool-btn';
        recordBtn.style.fontSize = '0.9rem';
        recordBtn.onclick = () => this.toggleRecording();

        // زر لقطة الشاشة
        const screenshotBtn = document.createElement('button');
        screenshotBtn.innerHTML = '<i class="fas fa-camera"></i> لقطة شاشة';
        screenshotBtn.className = 'tool-btn';
        screenshotBtn.style.fontSize = '0.9rem';
        screenshotBtn.onclick = () => this.takeScreenshot();

        // زر إيقاف المشاركة
        const stopBtn = document.createElement('button');
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
        stopBtn.className = 'tool-btn';
        stopBtn.style.fontSize = '0.9rem';
        stopBtn.style.background = '#ff4757';
        stopBtn.onclick = () => this.stopScreenShare();

        controlsDiv.appendChild(recordBtn);
        controlsDiv.appendChild(screenshotBtn);
        controlsDiv.appendChild(stopBtn);

        // تحديث منطقة العرض
        displayTitle.textContent = 'مشاركة الشاشة';
        displayContent.innerHTML = '';
        displayContent.appendChild(videoElement);
        displayContent.appendChild(controlsDiv);
        displayArea.style.display = 'flex';
    }

    // إعداد التسجيل
    setupRecording() {
        try {
            this.mediaRecorder = new MediaRecorder(this.mediaStream, {
                mimeType: 'video/webm;codecs=vp9'
            });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };

            console.log('✅ تم إعداد التسجيل');
        } catch (error) {
            console.error('❌ خطأ في إعداد التسجيل:', error);
        }
    }

    // تبديل التسجيل
    toggleRecording() {
        if (!this.mediaRecorder) {
            addMessageToChat('assistant', 'خطأ: لم يتم إعداد التسجيل بشكل صحيح');
            return;
        }

        if (this.mediaRecorder.state === 'recording') {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    // بدء التسجيل
    startRecording() {
        try {
            this.recordedChunks = [];
            this.mediaRecorder.start(1000); // تسجيل كل ثانية
            
            // تحديث زر التسجيل
            const recordBtn = document.querySelector('.screen-share-controls button');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-stop-circle"></i> إيقاف التسجيل';
                recordBtn.style.background = '#ff4757';
            }

            addMessageToChat('assistant', 'تم بدء تسجيل الشاشة');
            console.log('🔴 بدء تسجيل الشاشة');
        } catch (error) {
            console.error('❌ خطأ في بدء التسجيل:', error);
        }
    }

    // إيقاف التسجيل
    stopRecording() {
        try {
            this.mediaRecorder.stop();
            
            // تحديث زر التسجيل
            const recordBtn = document.querySelector('.screen-share-controls button');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> بدء التسجيل';
                recordBtn.style.background = '';
            }

            addMessageToChat('assistant', 'تم إيقاف التسجيل وحفظ الملف');
            console.log('⏹️ إيقاف تسجيل الشاشة');
        } catch (error) {
            console.error('❌ خطأ في إيقاف التسجيل:', error);
        }
    }

    // حفظ التسجيل
    saveRecording() {
        if (this.recordedChunks.length === 0) {
            console.warn('⚠️ لا توجد بيانات للحفظ');
            return;
        }

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `تسجيل_الشاشة_${new Date().toISOString().split('T')[0]}.webm`;
        a.click();
        
        URL.revokeObjectURL(url);
        console.log('💾 تم حفظ التسجيل');
    }

    // أخذ لقطة شاشة
    takeScreenshot() {
        try {
            const video = document.getElementById('sharedScreenVideo');
            if (!video) {
                addMessageToChat('assistant', 'خطأ: لا يمكن العثور على الفيديو');
                return;
            }

            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `لقطة_شاشة_${new Date().toISOString().split('T')[0]}.png`;
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/png');

            addMessageToChat('assistant', 'تم حفظ لقطة الشاشة');
            console.log('📸 تم أخذ لقطة شاشة');
        } catch (error) {
            console.error('❌ خطأ في أخذ لقطة الشاشة:', error);
        }
    }

    // إيقاف مشاركة الشاشة
    stopScreenShare() {
        try {
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
                this.mediaStream = null;
            }

            if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
                this.mediaRecorder.stop();
            }

            this.isSharing = false;
            
            // إخفاء منطقة العرض
            document.getElementById('displayArea').style.display = 'none';
            
            addMessageToChat('assistant', 'تم إيقاف مشاركة الشاشة');
            console.log('⏹️ تم إيقاف مشاركة الشاشة');
        } catch (error) {
            console.error('❌ خطأ في إيقاف مشاركة الشاشة:', error);
        }
    }

    // معالجة أخطاء مشاركة الشاشة
    handleScreenShareError(error) {
        let errorMessage = 'حدث خطأ في مشاركة الشاشة';
        
        switch (error.name) {
            case 'NotAllowedError':
                errorMessage = 'تم رفض الإذن لمشاركة الشاشة';
                break;
            case 'NotFoundError':
                errorMessage = 'لم يتم العثور على شاشة للمشاركة';
                break;
            case 'NotSupportedError':
                errorMessage = 'المتصفح لا يدعم مشاركة الشاشة';
                break;
            case 'AbortError':
                errorMessage = 'تم إلغاء مشاركة الشاشة';
                break;
        }
        
        addMessageToChat('assistant', `خطأ: ${errorMessage}`);
        console.error('❌', errorMessage, error);
    }

    // التحقق من دعم مشاركة الشاشة
    static isSupported() {
        return navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia;
    }
}

// إنشاء مثيل مدير مشاركة الشاشة
const screenShareManager = new ScreenShareManager();

// تصدير المدير للاستخدام العام
window.screenShareManager = screenShareManager;
window.ScreenShareManager = ScreenShareManager;
