// ملف script.js - يعتمد على assistant-core.js للوظائف الأساسية
// هذا الملف يحتوي على وظائف إضافية ومساعدة فقط

// المتغيرات العامة للتوافق مع الكود القديم
let currentConversation = [];
let assistantSettings = {
    language: 'ar-SA',
    speechRate: 1,
    speechVolume: 0.8,
    voiceEnabled: true
};

// تحديث currentConversation عند إضافة رسائل جديدة
function updateConversationHistory() {
    const history = window.conversationHistory || [];
    currentConversation = history.map(msg => ({
        sender: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
        type: 'text'
    }));
}

// وظائف مساعدة للتوافق مع الكود القديم
function addMessageToChat(sender, content) {
    // استخدام appendMessage من assistant-core.js
    if (window.appendMessage) {
        appendMessage(sender, content);
    }
}

// وظائف مساعدة أخرى
function toggleVoiceRecording() {
    if (window.startVoiceRecording) {
        startVoiceRecording();
    }
}

function toggleVoiceSettings() {
    if (window.openVoiceSettings) {
        openVoiceSettings();
    }
}

function closeSettingsModal() {
    if (window.closeSettings) {
        closeSettings();
    }
}

function closeDisplayArea() {
    const displayArea = document.getElementById('displayArea');
    if (displayArea) {
        displayArea.style.display = 'none';
    }
}

// تصدير الوظائف للتوافق
window.addMessageToChat = addMessageToChat;
window.updateConversationHistory = updateConversationHistory;
