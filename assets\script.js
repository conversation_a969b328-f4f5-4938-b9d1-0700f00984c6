// ملف script.js - وظائف مساعدة بسيطة

// وظائف للتوافق مع الكود القديم
function addMessageToChat(sender, content) {
    if (window.addMessage) {
        addMessage(sender, content);
    }
}

function toggleVoiceRecording() {
    if (window.startVoiceRecording) {
        startVoiceRecording();
    }
}

function toggleVoiceSettings() {
    if (window.openVoiceSettings) {
        openVoiceSettings();
    }
}

function closeSettingsModal() {
    if (window.closeSettings) {
        closeSettings();
    }
}

function closeDisplayArea() {
    const displayArea = document.getElementById('displayArea');
    if (displayArea) {
        displayArea.style.display = 'none';
    }
}

// تصدير للتوافق
window.addMessageToChat = addMessageToChat;
