// المتغيرات العامة
let isRecording = false;
let currentConversation = [];
let assistantSettings = {
    language: 'ar-SA',
    speechRate: 1,
    speechVolume: 0.8,
    voiceEnabled: true
};

// تهيئة المساعد
function initializeAssistant() {
    console.log('🤖 تهيئة المساعد التقني الذكي...');

    // ربط الأحداث
    bindEventListeners();

    // تحميل الإعدادات المحفوظة
    loadSettings();

    // تهيئة منطقة السحب والإفلات
    initializeFileDropZone();

    console.log('✅ تم تهيئة المساعد بنجاح');
}

// ربط مستمعي الأحداث
function bindEventListeners() {
    // زر الإرسال
    document.getElementById('sendBtn').addEventListener('click', sendMessage);

    // مفتاح Enter في حقل الإدخال
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    // زر التسجيل الصوتي
    document.getElementById('voiceRecordBtn').addEventListener('click', toggleVoiceRecording);

    // أزرار الأدوات
    document.getElementById('screenShareBtn').addEventListener('click', startScreenShare);
    document.getElementById('videoUploadBtn').addEventListener('click', uploadVideo);
    document.getElementById('videoAnalyzeBtn').addEventListener('click', analyzeVideo);
    document.getElementById('ar3dBtn').addEventListener('click', show3DView);
    document.getElementById('summaryBtn').addEventListener('click', generateSummary);
    document.getElementById('voiceBtn').addEventListener('click', toggleVoiceSettings);

    // إغلاق منطقة العرض
    document.getElementById('closeDisplay').addEventListener('click', closeDisplayArea);

    // إغلاق النوافذ المنبثقة
    document.getElementById('closeSettings').addEventListener('click', closeSettingsModal);
}

// إرسال رسالة
function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    addMessageToChat('user', message);

    // مسح حقل الإدخال
    messageInput.value = '';

    // معالجة الرسالة
    processUserMessage(message);
}

// إضافة رسالة للمحادثة
function addMessageToChat(sender, content, type = 'text') {
    const chatContainer = document.getElementById('chatContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (type === 'html') {
        messageContent.innerHTML = content;
    } else {
        messageContent.textContent = content;
    }

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);

    chatContainer.appendChild(messageDiv);

    // التمرير للأسفل
    chatContainer.scrollTop = chatContainer.scrollHeight;

    // حفظ في المحادثة
    currentConversation.push({
        sender: sender,
        content: content,
        timestamp: new Date().toISOString(),
        type: type
    });
}

// معالجة رسالة المستخدم
async function processUserMessage(message) {
    // إظهار مؤشر الكتابة
    showTypingIndicator();

    try {
        // تحليل نوع الطلب
        const requestType = analyzeRequestType(message);

        // معالجة حسب النوع
        let response = '';

        switch (requestType) {
            case 'greeting':
                response = getGreetingResponse();
                break;
            case 'technical':
                response = await getTechnicalResponse(message);
                break;
            case 'code':
                response = await getCodeResponse(message);
                break;
            case 'explanation':
                response = await getExplanationResponse(message);
                break;
            default:
                response = await getGeneralResponse(message);
        }

        // إزالة مؤشر الكتابة
        hideTypingIndicator();

        // إضافة رد المساعد
        addMessageToChat('assistant', response);

        // قراءة الرد صوتياً إذا كان مفعلاً
        if (assistantSettings.voiceEnabled) {
            speakText(response);
        }

    } catch (error) {
        hideTypingIndicator();
        addMessageToChat('assistant', 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.');
        console.error('خطأ في معالجة الرسالة:', error);
    }
}

// تحليل نوع الطلب
function analyzeRequestType(message) {
    const greetings = ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء'];
    const technical = ['كيف', 'ما هو', 'اشرح', 'وضح'];
    const code = ['كود', 'برمجة', 'function', 'class', 'script'];

    const lowerMessage = message.toLowerCase();

    if (greetings.some(word => lowerMessage.includes(word))) {
        return 'greeting';
    } else if (code.some(word => lowerMessage.includes(word))) {
        return 'code';
    } else if (technical.some(word => lowerMessage.includes(word))) {
        return 'technical';
    } else {
        return 'general';
    }
}

// ردود مختلفة حسب النوع
function getGreetingResponse() {
    const greetings = [
        'أهلاً وسهلاً بك! كيف يمكنني مساعدتك اليوم؟',
        'مرحباً! أنا هنا لمساعدتك في جميع احتياجاتك التقنية',
        'السلام عليكم! كيف يمكنني أن أكون مفيداً لك؟'
    ];
    return greetings[Math.floor(Math.random() * greetings.length)];
}

async function getTechnicalResponse(message) {
    // استخدام المساعد التقني الذكي
    if (window.technicalAssistant) {
        return technicalAssistant.getResponse(message);
    }
    return `بناءً على سؤالك التقني: "${message}"، إليك شرح مفصل:\n\nهذا مثال على رد تقني شامل يتضمن معلومات مفيدة ومفصلة حول الموضوع المطلوب.`;
}

async function getCodeResponse(message) {
    // استخدام المساعد التقني للكود
    if (window.technicalAssistant) {
        return technicalAssistant.getResponse(message);
    }
    return `إليك مثال على الكود المطلوب:\n\n\`\`\`javascript\n// مثال على كود JavaScript\nfunction example() {\n    console.log("مرحباً من المساعد التقني!");\n}\n\`\`\`\n\nهل تريد مني شرح هذا الكود أو تعديله؟`;
}

async function getExplanationResponse(message) {
    // استخدام المساعد التقني للشرح
    if (window.technicalAssistant) {
        return technicalAssistant.getResponse(message);
    }
    return `شرح مفصل للموضوع:\n\n1. النقطة الأولى\n2. النقطة الثانية\n3. النقطة الثالثة\n\nهل تريد المزيد من التفاصيل حول أي نقطة؟`;
}

async function getGeneralResponse(message) {
    // استخدام المساعد التقني العام
    if (window.technicalAssistant) {
        return technicalAssistant.getResponse(message);
    }
    return `شكراً لك على سؤالك. أفهم أنك تسأل عن: "${message}"\n\nإليك إجابة شاملة ومفيدة حول هذا الموضوع.`;
}

// مؤشر الكتابة
function showTypingIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'message assistant typing-indicator';
    indicator.id = 'typingIndicator';
    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;

    document.getElementById('chatContainer').appendChild(indicator);
    document.getElementById('chatContainer').scrollTop = document.getElementById('chatContainer').scrollHeight;
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// تبديل التسجيل الصوتي
function toggleVoiceRecording() {
    if (isRecording) {
        stopVoiceRecording();
    } else {
        startVoiceRecording();
    }
}

function startVoiceRecording() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('المتصفح لا يدعم التعرف على الكلام');
        return;
    }

    const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
    recognition.lang = assistantSettings.language;
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onstart = function() {
        isRecording = true;
        document.getElementById('voiceRecordBtn').classList.add('recording');
        console.log('🎤 بدء التسجيل الصوتي');
    };

    recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        document.getElementById('messageInput').value = transcript;
        console.log('🗣️ النص المسجل:', transcript);
    };

    recognition.onerror = function(event) {
        console.error('خطأ في التسجيل:', event.error);
        stopVoiceRecording();
    };

    recognition.onend = function() {
        stopVoiceRecording();
    };

    recognition.start();
}

function stopVoiceRecording() {
    isRecording = false;
    document.getElementById('voiceRecordBtn').classList.remove('recording');
    console.log('⏹️ توقف التسجيل الصوتي');
}

// قراءة النص صوتياً
function speakText(text) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = assistantSettings.language;
        utterance.rate = assistantSettings.speechRate;
        utterance.volume = assistantSettings.speechVolume;

        speechSynthesis.speak(utterance);
    } else if (typeof responsiveVoice !== 'undefined') {
        responsiveVoice.speak(text, assistantSettings.language, {
            rate: assistantSettings.speechRate,
            volume: assistantSettings.speechVolume
        });
    }
}

// تشغيل صوت الترحيب
function playWelcomeSound() {
    const audio = new Audio('voice/iraqi_greeting.mp3');
    audio.volume = assistantSettings.speechVolume;
    audio.play().catch(e => {
        console.log('لا يمكن تشغيل صوت الترحيب:', e);
        // بديل نصي
        speakText('يا قائم آل محمد، أهلاً وسهلاً بك في المساعد التقني الذكي');
    });
}

// تهيئة منطقة السحب والإفلات
function initializeFileDropZone() {
    const dropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');

    dropZone.addEventListener('click', () => fileInput.click());

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });

    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
}

// معالجة الملفات المرفوعة
function handleFiles(files) {
    Array.from(files).forEach(file => {
        console.log('📁 ملف مرفوع:', file.name, file.type);

        if (file.type.startsWith('video/')) {
            handleVideoFile(file);
        } else if (file.type.startsWith('audio/')) {
            handleAudioFile(file);
        } else if (file.type.startsWith('image/')) {
            handleImageFile(file);
        } else {
            handleDocumentFile(file);
        }
    });
}

// معالجة ملفات الفيديو
function handleVideoFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الفيديو: ${file.name}. جاري التحليل...`);
    // سيتم تنفيذ التحليل في videoAnalyzer.js
}

// معالجة ملفات الصوت
function handleAudioFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الصوت: ${file.name}. جاري المعالجة...`);
}

// معالجة الصور
function handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageHtml = `
            <p>تم تحميل الصورة: ${file.name}</p>
            <img src="${e.target.result}" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
        `;
        addMessageToChat('assistant', imageHtml, 'html');
    };
    reader.readAsDataURL(file);
}

// معالجة المستندات
function handleDocumentFile(file) {
    addMessageToChat('assistant', `تم تحميل المستند: ${file.name}. جاري القراءة والتحليل...`);
}

// حفظ وتحميل الإعدادات
function loadSettings() {
    const saved = localStorage.getItem('assistantSettings');
    if (saved) {
        assistantSettings = { ...assistantSettings, ...JSON.parse(saved) };
    }
}

function saveSettings() {
    localStorage.setItem('assistantSettings', JSON.stringify(assistantSettings));
}

// إغلاق منطقة العرض
function closeDisplayArea() {
    document.getElementById('displayArea').style.display = 'none';
}

// إغلاق نافذة الإعدادات
function closeSettingsModal() {
    document.getElementById('settingsModal').style.display = 'none';
}

// وظائف الأدوات (ستكون مرتبطة بالوحدات المختلفة)
function startScreenShare() {
    console.log('🖥️ بدء مشاركة الشاشة');
    addMessageToChat('assistant', 'جاري تفعيل مشاركة الشاشة...');
}

function uploadVideo() {
    console.log('📹 تحميل فيديو');
    document.getElementById('fileInput').click();
}

function analyzeVideo() {
    console.log('📊 تحليل فيديو');
    addMessageToChat('assistant', 'يرجى تحميل فيديو أولاً لتحليله');
}

function show3DView() {
    console.log('🎮 عرض ثلاثي الأبعاد');
    addMessageToChat('assistant', 'جاري تحميل العرض ثلاثي الأبعاد...');
}

function generateSummary() {
    console.log('📝 توليد ملخص');
    addMessageToChat('assistant', 'جاري توليد ملخص للمحادثة الحالية...');
}

function toggleVoiceSettings() {
    console.log('🔊 إعدادات الصوت');
    document.getElementById('settingsModal').style.display = 'flex';
}

// تشغيل صوت الترحيب
function playWelcomeSound() {
    const audio = new Audio('voice/iraqi_greeting.mp3');
    audio.volume = assistantSettings.speechVolume;
    audio.play().catch(e => {
        console.log('لا يمكن تشغيل صوت الترحيب:', e);
        // بديل نصي
        speakText('يا قائم آل محمد، أهلاً وسهلاً بك في المساعد التقني الذكي');
    });
}

// تهيئة منطقة السحب والإفلات
function initializeFileDropZone() {
    const dropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');
    
    dropZone.addEventListener('click', () => fileInput.click());
    
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });
    
    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
}

// معالجة الملفات المرفوعة
function handleFiles(files) {
    Array.from(files).forEach(file => {
        console.log('📁 ملف مرفوع:', file.name, file.type);
        
        if (file.type.startsWith('video/')) {
            handleVideoFile(file);
        } else if (file.type.startsWith('audio/')) {
            handleAudioFile(file);
        } else if (file.type.startsWith('image/')) {
            handleImageFile(file);
        } else {
            handleDocumentFile(file);
        }
    });
}

// معالجة ملفات الفيديو
function handleVideoFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الفيديو: ${file.name}. جاري التحليل...`);
    // سيتم تنفيذ التحليل في videoAnalyzer.js
}

// معالجة ملفات الصوت
function handleAudioFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الصوت: ${file.name}. جاري المعالجة...`);
}

// معالجة الصور
function handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageHtml = `
            <p>تم تحميل الصورة: ${file.name}</p>
            <img src="${e.target.result}" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
        `;
        addMessageToChat('assistant', imageHtml, 'html');
    };
    reader.readAsDataURL(file);
}

// معالجة المستندات
function handleDocumentFile(file) {
    addMessageToChat('assistant', `تم تحميل المستند: ${file.name}. جاري القراءة والتحليل...`);
}

// حفظ وتحميل الإعدادات
function loadSettings() {
    const saved = localStorage.getItem('assistantSettings');
    if (saved) {
        assistantSettings = { ...assistantSettings, ...JSON.parse(saved) };
    }
}

function saveSettings() {
    localStorage.setItem('assistantSettings', JSON.stringify(assistantSettings));
}

// إغلاق منطقة العرض
function closeDisplayArea() {
    document.getElementById('displayArea').style.display = 'none';
}

// إغلاق نافذة الإعدادات
function closeSettingsModal() {
    document.getElementById('settingsModal').style.display = 'none';
}
