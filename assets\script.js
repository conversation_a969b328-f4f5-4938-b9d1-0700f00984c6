// المتغيرات العامة
let isRecording = false;
let currentConversation = [];
let assistantSettings = {
    language: 'ar-SA',
    speechRate: 1,
    speechVolume: 0.8,
    voiceEnabled: true
};

// تهيئة المساعد
async function initializeAssistant() {
    console.log('🤖 تهيئة المساعد التقني الذكي...');

    // ربط الأحداث
    bindEventListeners();

    // تحميل الإعدادات المحفوظة
    loadSettings();

    // تهيئة منطقة السحب والإفلات
    initializeFileDropZone();

    // فحص الاتصال مع LM Studio
    if (window.technicalAssistant) {
        const isConnected = await technicalAssistant.checkConnection();
        if (isConnected) {
            console.log('✅ تم الاتصال بـ LM Studio بنجاح');
            updateConnectionStatus(true);
        } else {
            console.log('⚠️ لم يتم الاتصال بـ LM Studio');
            updateConnectionStatus(false);
        }
    }

    // تحديث حالة الأصوات المتاحة
    if (window.speechSynthesis) {
        speechSynthesis.onvoiceschanged = () => {
            const voices = speechSynthesis.getVoices();
            console.log('🔊 الأصوات المتاحة:', voices.length);
        };
    }

    console.log('✅ تم تهيئة المساعد بنجاح');
}

// تحديث حالة الاتصال في الواجهة
function updateConnectionStatus(isConnected) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-indicator span:last-child');

    if (statusDot && statusText) {
        if (isConnected) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }
}

// ربط مستمعي الأحداث
function bindEventListeners() {
    // زر الإرسال
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        sendBtn.addEventListener('click', sendMessage);
    }

    // مفتاح Enter في حقل الإدخال
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }

    // زر التسجيل الصوتي
    const voiceRecordBtn = document.getElementById('voiceRecordBtn');
    if (voiceRecordBtn) {
        voiceRecordBtn.addEventListener('click', toggleVoiceRecording);
    }

    // أزرار الأدوات
    const screenShareBtn = document.getElementById('screenShareBtn');
    if (screenShareBtn) {
        screenShareBtn.addEventListener('click', startScreenShare);
    }

    const videoUploadBtn = document.getElementById('videoUploadBtn');
    if (videoUploadBtn) {
        videoUploadBtn.addEventListener('click', uploadVideo);
    }

    const videoAnalyzeBtn = document.getElementById('videoAnalyzeBtn');
    if (videoAnalyzeBtn) {
        videoAnalyzeBtn.addEventListener('click', analyzeVideo);
    }

    const ar3dBtn = document.getElementById('ar3dBtn');
    if (ar3dBtn) {
        ar3dBtn.addEventListener('click', show3DView);
    }

    const summaryBtn = document.getElementById('summaryBtn');
    if (summaryBtn) {
        summaryBtn.addEventListener('click', generateSummary);
    }

    const voiceBtn = document.getElementById('voiceBtn');
    if (voiceBtn) {
        voiceBtn.addEventListener('click', toggleVoiceSettings);
    }

    // إغلاق منطقة العرض
    const closeDisplay = document.getElementById('closeDisplay');
    if (closeDisplay) {
        closeDisplay.addEventListener('click', closeDisplayArea);
    }

    // إغلاق النوافذ المنبثقة
    const closeSettings = document.getElementById('closeSettings');
    if (closeSettings) {
        closeSettings.addEventListener('click', closeSettingsModal);
    }

    console.log('✅ تم ربط جميع مستمعي الأحداث');
}

// إرسال رسالة
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    addMessageToChat('user', message);

    // مسح حقل الإدخال
    messageInput.value = '';

    // إظهار مؤشر الكتابة
    showTypingIndicator();

    try {
        // استخدام المساعد التقني مباشرة
        let response = '';
        if (window.technicalAssistant) {
            response = await technicalAssistant.getResponse(message);
        } else {
            response = 'المساعد التقني غير متاح حالياً. يرجى المحاولة لاحقاً.';
        }

        // إزالة مؤشر الكتابة
        hideTypingIndicator();

        // إضافة رد المساعد
        addMessageToChat('assistant', response);

        // قراءة الرد صوتياً إذا كان مفعلاً
        if (assistantSettings.voiceEnabled) {
            speakText(response);
        }

    } catch (error) {
        hideTypingIndicator();
        addMessageToChat('assistant', 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.');
        console.error('خطأ في إرسال الرسالة:', error);
    }
}

// إضافة رسالة للمحادثة
function addMessageToChat(sender, content, type = 'text') {
    const chatContainer = document.getElementById('chatContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (type === 'html') {
        messageContent.innerHTML = content;
    } else {
        messageContent.textContent = content;
    }

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);

    chatContainer.appendChild(messageDiv);

    // التمرير للأسفل
    chatContainer.scrollTop = chatContainer.scrollHeight;

    // حفظ في المحادثة
    currentConversation.push({
        sender: sender,
        content: content,
        timestamp: new Date().toISOString(),
        type: type
    });
}

// معالجة رسالة المستخدم
async function processUserMessage(message) {
    // إظهار مؤشر الكتابة
    showTypingIndicator();

    try {
        // تحليل نوع الطلب
        const requestType = analyzeRequestType(message);

        // معالجة حسب النوع
        let response = '';

        switch (requestType) {
            case 'greeting':
                response = getGreetingResponse();
                break;
            case 'technical':
                response = await getTechnicalResponse(message);
                break;
            case 'code':
                response = await getCodeResponse(message);
                break;
            case 'explanation':
                response = await getExplanationResponse(message);
                break;
            default:
                response = await getGeneralResponse(message);
        }

        // إزالة مؤشر الكتابة
        hideTypingIndicator();

        // إضافة رد المساعد
        addMessageToChat('assistant', response);

        // قراءة الرد صوتياً إذا كان مفعلاً
        if (assistantSettings.voiceEnabled) {
            speakText(response);
        }

    } catch (error) {
        hideTypingIndicator();
        addMessageToChat('assistant', 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.');
        console.error('خطأ في معالجة الرسالة:', error);
    }
}

// تحليل نوع الطلب
function analyzeRequestType(message) {
    const greetings = ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء'];
    const technical = ['كيف', 'ما هو', 'اشرح', 'وضح'];
    const code = ['كود', 'برمجة', 'function', 'class', 'script'];

    const lowerMessage = message.toLowerCase();

    if (greetings.some(word => lowerMessage.includes(word))) {
        return 'greeting';
    } else if (code.some(word => lowerMessage.includes(word))) {
        return 'code';
    } else if (technical.some(word => lowerMessage.includes(word))) {
        return 'technical';
    } else {
        return 'general';
    }
}

// ردود مختلفة حسب النوع
function getGreetingResponse() {
    const greetings = [
        'أهلاً وسهلاً بك! كيف يمكنني مساعدتك اليوم؟',
        'مرحباً! أنا هنا لمساعدتك في جميع احتياجاتك التقنية',
        'السلام عليكم! كيف يمكنني أن أكون مفيداً لك؟'
    ];
    return greetings[Math.floor(Math.random() * greetings.length)];
}

async function getTechnicalResponse(message) {
    // استخدام المساعد التقني الذكي
    if (window.technicalAssistant) {
        return await technicalAssistant.getResponse(message);
    }
    return `بناءً على سؤالك التقني: "${message}"، إليك شرح مفصل:\n\nهذا مثال على رد تقني شامل يتضمن معلومات مفيدة ومفصلة حول الموضوع المطلوب.`;
}

async function getCodeResponse(message) {
    // استخدام المساعد التقني للكود
    if (window.technicalAssistant) {
        return await technicalAssistant.getResponse(message);
    }
    return `إليك مثال على الكود المطلوب:\n\n\`\`\`javascript\n// مثال على كود JavaScript\nfunction example() {\n    console.log("مرحباً من المساعد التقني!");\n}\n\`\`\`\n\nهل تريد مني شرح هذا الكود أو تعديله؟`;
}

async function getExplanationResponse(message) {
    // استخدام المساعد التقني للشرح
    if (window.technicalAssistant) {
        return await technicalAssistant.getResponse(message);
    }
    return `شرح مفصل للموضوع:\n\n1. النقطة الأولى\n2. النقطة الثانية\n3. النقطة الثالثة\n\nهل تريد المزيد من التفاصيل حول أي نقطة؟`;
}

async function getGeneralResponse(message) {
    // استخدام المساعد التقني العام
    if (window.technicalAssistant) {
        return await technicalAssistant.getResponse(message);
    }
    return `شكراً لك على سؤالك. أفهم أنك تسأل عن: "${message}"\n\nإليك إجابة شاملة ومفيدة حول هذا الموضوع.`;
}

// مؤشر الكتابة
function showTypingIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'message assistant typing-indicator';
    indicator.id = 'typingIndicator';
    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;

    document.getElementById('chatContainer').appendChild(indicator);
    document.getElementById('chatContainer').scrollTop = document.getElementById('chatContainer').scrollHeight;
}

function hideTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// تبديل التسجيل الصوتي
function toggleVoiceRecording() {
    if (window.voiceAssistant) {
        if (voiceAssistant.isListening) {
            voiceAssistant.stopListening();
        } else {
            voiceAssistant.startListening();
        }
    } else {
        // نسخة احتياطية بسيطة
        if (isRecording) {
            stopVoiceRecording();
        } else {
            startVoiceRecording();
        }
    }
}

function startVoiceRecording() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('المتصفح لا يدعم التعرف على الكلام');
        return;
    }

    const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
    recognition.lang = assistantSettings.language;
    recognition.continuous = false;
    recognition.interimResults = false;

    recognition.onstart = function() {
        isRecording = true;
        document.getElementById('voiceRecordBtn').classList.add('recording');
        console.log('🎤 بدء التسجيل الصوتي');
    };

    recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        document.getElementById('messageInput').value = transcript;
        console.log('🗣️ النص المسجل:', transcript);

        // إرسال الرسالة تلقائياً إذا كانت تحتوي على سؤال
        if (transcript.includes('؟') || transcript.includes('كيف') || transcript.includes('ما هو')) {
            setTimeout(() => sendMessage(), 500);
        }
    };

    recognition.onerror = function(event) {
        console.error('خطأ في التسجيل:', event.error);
        stopVoiceRecording();
    };

    recognition.onend = function() {
        stopVoiceRecording();
    };

    recognition.start();
}

function stopVoiceRecording() {
    isRecording = false;
    document.getElementById('voiceRecordBtn').classList.remove('recording');
    console.log('⏹️ توقف التسجيل الصوتي');
}

// قراءة النص صوتياً
function speakText(text) {
    if (!text || text.trim() === '') return;

    // تنظيف النص من الرموز غير المرغوبة
    const cleanText = text.replace(/[*#`]/g, '').replace(/\n+/g, ' ').trim();

    // محاولة استخدام ResponsiveVoice أولاً
    if (typeof responsiveVoice !== 'undefined' && responsiveVoice.voiceSupport()) {
        const voiceMap = {
            'ar-SA': 'Arabic Female',
            'ar-IQ': 'Arabic Female',
            'en-US': 'US English Female'
        };

        const voiceName = voiceMap[assistantSettings.language] || 'Arabic Female';

        responsiveVoice.speak(cleanText, voiceName, {
            rate: assistantSettings.speechRate || 1,
            volume: assistantSettings.speechVolume || 0.8,
            onstart: () => console.log('🔊 بدء قراءة النص'),
            onend: () => console.log('✅ انتهاء قراءة النص'),
            onerror: (error) => {
                console.error('❌ خطأ في ResponsiveVoice:', error);
                // استخدام speechSynthesis كبديل
                fallbackToSpeechSynthesis(cleanText);
            }
        });
    } else if ('speechSynthesis' in window) {
        fallbackToSpeechSynthesis(cleanText);
    } else {
        console.warn('⚠️ لا يوجد دعم للنطق في هذا المتصفح');
    }
}

// وظيفة بديلة لاستخدام speechSynthesis
function fallbackToSpeechSynthesis(text) {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = assistantSettings.language || 'ar-SA';
    utterance.rate = assistantSettings.speechRate || 1;
    utterance.volume = assistantSettings.speechVolume || 0.8;
    utterance.pitch = 1;

    // البحث عن صوت عربي
    const voices = speechSynthesis.getVoices();
    const arabicVoice = voices.find(voice =>
        voice.lang.startsWith('ar') ||
        voice.name.toLowerCase().includes('arabic')
    );

    if (arabicVoice) {
        utterance.voice = arabicVoice;
        console.log('🔊 استخدام الصوت:', arabicVoice.name);
    }

    utterance.onstart = () => console.log('🔊 بدء قراءة النص');
    utterance.onend = () => console.log('✅ انتهاء قراءة النص');
    utterance.onerror = (event) => console.error('❌ خطأ في النطق:', event.error);

    speechSynthesis.speak(utterance);
}

// تشغيل صوت الترحيب
function playWelcomeSound() {
    const audio = new Audio('voice/iraqi_greeting.mp3');
    audio.volume = assistantSettings.speechVolume;
    audio.play().catch(e => {
        console.log('لا يمكن تشغيل صوت الترحيب:', e);
        // بديل نصي
        speakText('يا قائم آل محمد، أهلاً وسهلاً بك في المساعد التقني الذكي');
    });
}

// تهيئة منطقة السحب والإفلات
function initializeFileDropZone() {
    const dropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');

    dropZone.addEventListener('click', () => fileInput.click());

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });

    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
}

// معالجة الملفات المرفوعة
function handleFiles(files) {
    Array.from(files).forEach(file => {
        console.log('📁 ملف مرفوع:', file.name, file.type);

        if (file.type.startsWith('video/')) {
            handleVideoFile(file);
        } else if (file.type.startsWith('audio/')) {
            handleAudioFile(file);
        } else if (file.type.startsWith('image/')) {
            handleImageFile(file);
        } else {
            handleDocumentFile(file);
        }
    });
}

// معالجة ملفات الفيديو
function handleVideoFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الفيديو: ${file.name}. جاري التحليل...`);

    if (window.videoAnalyzer) {
        videoAnalyzer.analyzeVideo(file);
    } else {
        // معالجة بسيطة إذا لم تكن الوحدة متاحة
        setTimeout(() => {
            addMessageToChat('assistant', `تم تحليل الفيديو: ${file.name}\n\nالمعلومات الأساسية:\n• الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB\n• النوع: ${file.type}\n\nيمكنك استخدام أدوات التحليل المتقدمة من الشريط الجانبي.`);
        }, 2000);
    }
}

// معالجة ملفات الصوت
function handleAudioFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الصوت: ${file.name}. جاري المعالجة...`);
}

// معالجة الصور
function handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageHtml = `
            <p>تم تحميل الصورة: ${file.name}</p>
            <img src="${e.target.result}" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
        `;
        addMessageToChat('assistant', imageHtml, 'html');
    };
    reader.readAsDataURL(file);
}

// معالجة المستندات
function handleDocumentFile(file) {
    addMessageToChat('assistant', `تم تحميل المستند: ${file.name}. جاري القراءة والتحليل...`);
}

// حفظ وتحميل الإعدادات
function loadSettings() {
    const saved = localStorage.getItem('assistantSettings');
    if (saved) {
        assistantSettings = { ...assistantSettings, ...JSON.parse(saved) };
    }
}

function saveSettings() {
    localStorage.setItem('assistantSettings', JSON.stringify(assistantSettings));
}

// إغلاق منطقة العرض
function closeDisplayArea() {
    document.getElementById('displayArea').style.display = 'none';
}

// إغلاق نافذة الإعدادات
function closeSettingsModal() {
    document.getElementById('settingsModal').style.display = 'none';
}

// وظائف الأدوات (مرتبطة بالوحدات المختلفة)
async function startScreenShare() {
    console.log('🖥️ بدء مشاركة الشاشة');

    // التحقق من دعم المتصفح
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        addMessageToChat('assistant', 'عذراً، المتصفح لا يدعم مشاركة الشاشة');
        return;
    }

    try {
        addMessageToChat('assistant', 'جاري تفعيل مشاركة الشاشة...');

        const stream = await navigator.mediaDevices.getDisplayMedia({
            video: {
                mediaSource: 'screen',
                width: { ideal: 1920 },
                height: { ideal: 1080 },
                frameRate: { ideal: 30 }
            },
            audio: true
        });

        // عرض الشاشة المشاركة
        displaySharedScreen(stream);

        addMessageToChat('assistant', 'تم بدء مشاركة الشاشة بنجاح! يمكنك رؤية الشاشة في منطقة العرض.');

    } catch (error) {
        console.error('خطأ في مشاركة الشاشة:', error);
        let errorMessage = 'حدث خطأ في مشاركة الشاشة';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن لمشاركة الشاشة';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على شاشة للمشاركة';
        }

        addMessageToChat('assistant', errorMessage);
    }
}

// عرض الشاشة المشاركة
function displaySharedScreen(stream) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    // إنشاء عنصر الفيديو
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';
    video.style.height = 'auto';
    video.style.borderRadius = '8px';

    // إنشاء أزرار التحكم
    const controls = document.createElement('div');
    controls.style.marginTop = '15px';
    controls.style.display = 'flex';
    controls.style.gap = '10px';
    controls.style.justifyContent = 'center';

    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
    stopBtn.className = 'tool-btn';
    stopBtn.style.background = '#ff4757';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        displayArea.style.display = 'none';
        addMessageToChat('assistant', 'تم إيقاف مشاركة الشاشة');
    };

    controls.appendChild(stopBtn);

    // تحديث منطقة العرض
    displayTitle.textContent = 'مشاركة الشاشة';
    displayContent.innerHTML = '';
    displayContent.appendChild(video);
    displayContent.appendChild(controls);
    displayArea.style.display = 'flex';

    // مراقبة إيقاف المشاركة
    stream.getVideoTracks()[0].addEventListener('ended', () => {
        displayArea.style.display = 'none';
        addMessageToChat('assistant', 'تم إيقاف مشاركة الشاشة');
    });
}

function uploadVideo() {
    console.log('📹 تحميل فيديو');
    const fileInput = document.getElementById('fileInput');
    fileInput.accept = 'video/*';
    fileInput.onchange = function(e) {
        const file = e.target.files[0];
        if (file && file.type.startsWith('video/')) {
            handleVideoFile(file);
        } else {
            addMessageToChat('assistant', 'يرجى اختيار ملف فيديو صالح');
        }
    };
    fileInput.click();
}

function analyzeVideo() {
    console.log('📊 تحليل فيديو');
    const fileInput = document.getElementById('fileInput');
    fileInput.accept = 'video/*';
    fileInput.onchange = function(e) {
        const file = e.target.files[0];
        if (file && file.type.startsWith('video/')) {
            analyzeVideoFile(file);
        } else {
            addMessageToChat('assistant', 'يرجى اختيار ملف فيديو صالح');
        }
    };
    fileInput.click();
}

// معالجة ملف الفيديو المرفوع
function handleVideoFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الفيديو: ${file.name}`);

    // عرض معلومات أساسية عن الفيديو
    const fileInfo = `
📹 **معلومات الفيديو:**
• الاسم: ${file.name}
• الحجم: ${(file.size / 1024 / 1024).toFixed(2)} MB
• النوع: ${file.type}
• آخر تعديل: ${new Date(file.lastModified).toLocaleDateString('ar-SA')}

يمكنك الآن استخدام "تحليل فيديو" للحصول على تحليل مفصل.
    `;

    addMessageToChat('assistant', fileInfo);

    // حفظ الملف للتحليل لاحقاً
    window.currentVideoFile = file;
}

// تحليل ملف الفيديو
async function analyzeVideoFile(file) {
    addMessageToChat('assistant', `جاري تحليل الفيديو: ${file.name}...`);

    try {
        // إنشاء عنصر فيديو لاستخراج المعلومات
        const video = document.createElement('video');
        const url = URL.createObjectURL(file);
        video.src = url;

        video.onloadedmetadata = () => {
            const analysis = `
📊 **تحليل الفيديو المفصل:**

📋 **المعلومات الأساسية:**
• المدة: ${formatDuration(video.duration)}
• الأبعاد: ${video.videoWidth} × ${video.videoHeight}
• نسبة العرض إلى الارتفاع: ${(video.videoWidth / video.videoHeight).toFixed(2)}
• معدل الإطارات المقدر: 30 fps

🎬 **تحليل المحتوى:**
• إجمالي الإطارات المقدر: ${Math.floor(video.duration * 30)}
• نقاط التغيير المحتملة: ${Math.floor(video.duration / 10)}
• مناسب للتحليل: نعم

💡 **اقتراحات:**
• يمكن استخراج الصوت للتحليل النصي
• مناسب لإنشاء ملخص زمني
• يمكن تقسيمه إلى مقاطع للتحليل التفصيلي
            `;

            addMessageToChat('assistant', analysis);

            // عرض الفيديو في منطقة العرض
            displayVideoInViewer(video, file.name);

            URL.revokeObjectURL(url);
        };

        video.onerror = () => {
            addMessageToChat('assistant', 'حدث خطأ في تحليل الفيديو. تأكد من أن الملف صالح.');
            URL.revokeObjectURL(url);
        };

    } catch (error) {
        console.error('خطأ في تحليل الفيديو:', error);
        addMessageToChat('assistant', 'حدث خطأ في تحليل الفيديو.');
    }
}

// عرض الفيديو في منطقة العرض
function displayVideoInViewer(video, fileName) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    video.controls = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';

    const controls = document.createElement('div');
    controls.style.marginTop = '15px';
    controls.style.display = 'flex';
    controls.style.gap = '10px';
    controls.style.justifyContent = 'center';

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
    };

    controls.appendChild(closeBtn);

    displayTitle.textContent = `عرض الفيديو: ${fileName}`;
    displayContent.innerHTML = '';
    displayContent.appendChild(video);
    displayContent.appendChild(controls);
    displayArea.style.display = 'flex';
}

// تنسيق المدة
function formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

function show3DView() {
    console.log('🎮 عرض ثلاثي الأبعاد');

    // التحقق من دعم WebGL
    if (!window.WebGLRenderingContext) {
        addMessageToChat('assistant', 'عذراً، المتصفح لا يدعم WebGL للعرض ثلاثي الأبعاد');
        return;
    }

    addMessageToChat('assistant', 'جاري تحميل العرض ثلاثي الأبعاد...');

    try {
        // إنشاء مشهد ثلاثي الأبعاد بسيط
        create3DScene();
        addMessageToChat('assistant', 'تم تفعيل العرض ثلاثي الأبعاد! يمكنك التفاعل مع النموذج باستخدام الماوس.');
    } catch (error) {
        console.error('خطأ في العرض ثلاثي الأبعاد:', error);
        addMessageToChat('assistant', 'حدث خطأ في تحميل العرض ثلاثي الأبعاد');
    }
}

// إنشاء مشهد ثلاثي الأبعاد
function create3DScene() {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    // إنشاء المشهد
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);

    // إنشاء الكاميرا
    const camera = new THREE.PerspectiveCamera(75, 400 / 300, 0.1, 1000);
    camera.position.set(5, 5, 5);

    // إنشاء المُعرِض
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(400, 300);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // إضافة الإضاءة
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // إنشاء نموذج مكعب ملون
    const geometry = new THREE.BoxGeometry(2, 2, 2);
    const materials = [
        new THREE.MeshLambertMaterial({ color: 0xff0000 }), // أحمر
        new THREE.MeshLambertMaterial({ color: 0x00ff00 }), // أخضر
        new THREE.MeshLambertMaterial({ color: 0x0000ff }), // أزرق
        new THREE.MeshLambertMaterial({ color: 0xffff00 }), // أصفر
        new THREE.MeshLambertMaterial({ color: 0xff00ff }), // بنفسجي
        new THREE.MeshLambertMaterial({ color: 0x00ffff })  // سماوي
    ];

    const cube = new THREE.Mesh(geometry, materials);
    cube.castShadow = true;
    cube.receiveShadow = true;
    scene.add(cube);

    // إضافة أرضية
    const floorGeometry = new THREE.PlaneGeometry(20, 20);
    const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.rotation.x = -Math.PI / 2;
    floor.position.y = -2;
    floor.receiveShadow = true;
    scene.add(floor);

    // إنشاء أزرار التحكم
    const controlsDiv = document.createElement('div');
    controlsDiv.style.marginTop = '15px';
    controlsDiv.style.display = 'flex';
    controlsDiv.style.gap = '10px';
    controlsDiv.style.flexWrap = 'wrap';
    controlsDiv.style.justifyContent = 'center';

    let autoRotate = false;

    const rotateBtn = document.createElement('button');
    rotateBtn.innerHTML = '<i class="fas fa-sync"></i> دوران تلقائي';
    rotateBtn.className = 'tool-btn';
    rotateBtn.style.fontSize = '0.8rem';
    rotateBtn.onclick = () => {
        autoRotate = !autoRotate;
        rotateBtn.innerHTML = autoRotate ?
            '<i class="fas fa-pause"></i> إيقاف الدوران' :
            '<i class="fas fa-sync"></i> دوران تلقائي';
    };

    const colorBtn = document.createElement('button');
    colorBtn.innerHTML = '<i class="fas fa-palette"></i> تغيير الألوان';
    colorBtn.className = 'tool-btn';
    colorBtn.style.fontSize = '0.8rem';
    colorBtn.onclick = () => {
        const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff, 0xffa500, 0x800080];
        cube.material.forEach(material => {
            material.color.setHex(colors[Math.floor(Math.random() * colors.length)]);
        });
    };

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.fontSize = '0.8rem';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
        // تنظيف الموارد
        renderer.dispose();
        scene.clear();
    };

    controlsDiv.appendChild(rotateBtn);
    controlsDiv.appendChild(colorBtn);
    controlsDiv.appendChild(closeBtn);

    // تحديث منطقة العرض
    displayTitle.textContent = 'العرض ثلاثي الأبعاد';
    displayContent.innerHTML = '';
    displayContent.appendChild(renderer.domElement);
    displayContent.appendChild(controlsDiv);
    displayArea.style.display = 'flex';

    // حلقة العرض
    function animate() {
        requestAnimationFrame(animate);

        if (autoRotate) {
            cube.rotation.y += 0.01;
            cube.rotation.x += 0.005;
        }

        camera.lookAt(cube.position);
        renderer.render(scene, camera);
    }

    animate();

    // إضافة تفاعل الماوس
    let mouseDown = false;
    let mouseX = 0;
    let mouseY = 0;

    renderer.domElement.addEventListener('mousedown', (event) => {
        mouseDown = true;
        mouseX = event.clientX;
        mouseY = event.clientY;
    });

    renderer.domElement.addEventListener('mouseup', () => {
        mouseDown = false;
    });

    renderer.domElement.addEventListener('mousemove', (event) => {
        if (!mouseDown) return;

        const deltaX = event.clientX - mouseX;
        const deltaY = event.clientY - mouseY;

        cube.rotation.y += deltaX * 0.01;
        cube.rotation.x += deltaY * 0.01;

        mouseX = event.clientX;
        mouseY = event.clientY;
    });

    // تكبير/تصغير بعجلة الماوس
    renderer.domElement.addEventListener('wheel', (event) => {
        event.preventDefault();
        const scale = event.deltaY > 0 ? 1.1 : 0.9;
        camera.position.multiplyScalar(scale);
    });
}

function generateSummary() {
    console.log('📝 توليد ملخص');

    if (!currentConversation || currentConversation.length === 0) {
        addMessageToChat('assistant', 'لا توجد محادثة لتلخيصها. ابدأ محادثة أولاً!');
        return;
    }

    addMessageToChat('assistant', 'جاري توليد ملخص للمحادثة الحالية...');

    // توليد الملخص
    const summary = createConversationSummary();

    // عرض الملخص في منطقة العرض
    displaySummary(summary);

    addMessageToChat('assistant', 'تم إنشاء ملخص المحادثة! يمكنك مراجعته في منطقة العرض.');
}

// إنشاء ملخص المحادثة
function createConversationSummary() {
    const userMessages = currentConversation.filter(msg => msg.sender === 'user');
    const assistantMessages = currentConversation.filter(msg => msg.sender === 'assistant');

    // تحليل المواضيع
    const topics = extractTopicsFromConversation();

    // حساب الإحصائيات
    const totalMessages = currentConversation.length;
    const startTime = currentConversation[0]?.timestamp;
    const endTime = currentConversation[currentConversation.length - 1]?.timestamp;
    const duration = calculateConversationDuration(startTime, endTime);

    // بناء الملخص
    let summary = `📊 **ملخص المحادثة**\n\n`;

    summary += `📈 **إحصائيات:**\n`;
    summary += `• إجمالي الرسائل: ${totalMessages}\n`;
    summary += `• رسائل المستخدم: ${userMessages.length}\n`;
    summary += `• رسائل المساعد: ${assistantMessages.length}\n`;
    summary += `• مدة المحادثة: ${duration}\n\n`;

    if (topics.length > 0) {
        summary += `🎯 **المواضيع المطروحة:**\n`;
        topics.forEach(topic => {
            summary += `• ${topic}\n`;
        });
        summary += '\n';
    }

    // أهم الأسئلة
    const importantQuestions = userMessages
        .filter(msg => msg.content.length > 20)
        .slice(-5)
        .map(msg => msg.content.substring(0, 100) + (msg.content.length > 100 ? '...' : ''));

    if (importantQuestions.length > 0) {
        summary += `❓ **أهم الأسئلة:**\n`;
        importantQuestions.forEach((question, index) => {
            summary += `${index + 1}. ${question}\n`;
        });
        summary += '\n';
    }

    summary += `💡 **التوصيات:**\n`;
    summary += `• حفظ المحادثة للمراجعة لاحقاً\n`;
    summary += `• مراجعة النقاط المهمة المذكورة\n`;
    if (topics.includes('برمجة')) {
        summary += `• تطبيق أمثلة الكود المقدمة\n`;
    }
    summary += `• طرح أسئلة إضافية عند الحاجة\n`;

    return summary;
}

// استخراج المواضيع من المحادثة
function extractTopicsFromConversation() {
    const topics = new Set();
    const keywords = {
        'برمجة': ['javascript', 'python', 'html', 'css', 'كود', 'برمجة', 'function', 'class'],
        'شبكات': ['شبكة', 'network', 'إنترنت', 'بروتوكول', 'ip'],
        'قواعد البيانات': ['database', 'sql', 'قاعدة بيانات', 'جدول'],
        'أمان': ['security', 'أمان', 'تشفير', 'حماية'],
        'تصميم': ['design', 'تصميم', 'واجهة', 'ui', 'ux'],
        'ذكاء اصطناعي': ['ai', 'ذكاء اصطناعي', 'machine learning', 'تعلم آلة']
    };

    currentConversation.forEach(msg => {
        const content = msg.content.toLowerCase();
        for (const [topic, keywordList] of Object.entries(keywords)) {
            if (keywordList.some(keyword => content.includes(keyword))) {
                topics.add(topic);
            }
        }
    });

    return Array.from(topics);
}

// حساب مدة المحادثة
function calculateConversationDuration(startTime, endTime) {
    if (!startTime || !endTime) return 'غير محدد';

    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end - start;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'أقل من دقيقة';
    if (diffMins < 60) return `${diffMins} دقيقة`;

    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    return `${hours} ساعة و ${mins} دقيقة`;
}

// عرض الملخص في منطقة العرض
function displaySummary(summary) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    // تحويل النص إلى HTML
    const htmlSummary = summary
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/^• (.+)$/gm, '<li>$1</li>')
        .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^(.+)$/gm, '<p>$1</p>')
        .replace(/<p><\/p>/g, '');

    const summaryContainer = document.createElement('div');
    summaryContainer.style.padding = '20px';
    summaryContainer.style.lineHeight = '1.6';
    summaryContainer.innerHTML = htmlSummary;

    // أزرار التحكم
    const controlsDiv = document.createElement('div');
    controlsDiv.style.marginTop = '20px';
    controlsDiv.style.display = 'flex';
    controlsDiv.style.gap = '10px';

    const copyBtn = document.createElement('button');
    copyBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ الملخص';
    copyBtn.className = 'tool-btn';
    copyBtn.onclick = () => {
        navigator.clipboard.writeText(summary).then(() => {
            addMessageToChat('assistant', 'تم نسخ الملخص إلى الحافظة');
        });
    };

    const saveBtn = document.createElement('button');
    saveBtn.innerHTML = '<i class="fas fa-download"></i> حفظ الملخص';
    saveBtn.className = 'tool-btn';
    saveBtn.onclick = () => {
        const blob = new Blob([summary], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ملخص_المحادثة_${new Date().toISOString().split('T')[0]}.txt`;
        a.click();
        URL.revokeObjectURL(url);
        addMessageToChat('assistant', 'تم حفظ الملخص بنجاح');
    };

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
    };

    controlsDiv.appendChild(copyBtn);
    controlsDiv.appendChild(saveBtn);
    controlsDiv.appendChild(closeBtn);
    summaryContainer.appendChild(controlsDiv);

    // عرض الملخص
    displayTitle.textContent = 'ملخص المحادثة';
    displayContent.innerHTML = '';
    displayContent.appendChild(summaryContainer);
    displayArea.style.display = 'flex';
}

function toggleVoiceSettings() {
    console.log('🔊 إعدادات الصوت');
    const modal = document.getElementById('settingsModal');
    modal.style.display = 'flex';

    // ربط الإعدادات بالقيم الحالية
    const voiceLanguage = document.getElementById('voiceLanguage');
    const speechRate = document.getElementById('speechRate');
    const speechVolume = document.getElementById('speechVolume');

    if (voiceLanguage) voiceLanguage.value = assistantSettings.language;
    if (speechRate) speechRate.value = assistantSettings.speechRate;
    if (speechVolume) speechVolume.value = assistantSettings.speechVolume;

    // ربط أحداث التغيير
    if (voiceLanguage) {
        voiceLanguage.addEventListener('change', function() {
            assistantSettings.language = this.value;
            saveSettings();
            addMessageToChat('assistant', `تم تغيير اللغة إلى: ${this.options[this.selectedIndex].text}`);
        });
    }

    if (speechRate) {
        speechRate.addEventListener('input', function() {
            assistantSettings.speechRate = parseFloat(this.value);
            saveSettings();
        });
    }

    if (speechVolume) {
        speechVolume.addEventListener('input', function() {
            assistantSettings.speechVolume = parseFloat(this.value);
            saveSettings();
        });
    }
}

// إضافة الوظائف المفقودة
function initializeFileDropZone() {
    const dropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');

    if (!dropZone || !fileInput) return;

    dropZone.addEventListener('click', () => fileInput.click());

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });

    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
}

function handleFiles(files) {
    Array.from(files).forEach(file => {
        console.log('📁 ملف مرفوع:', file.name, file.type);

        if (file.type.startsWith('video/')) {
            handleVideoFile(file);
        } else if (file.type.startsWith('audio/')) {
            handleAudioFile(file);
        } else if (file.type.startsWith('image/')) {
            handleImageFile(file);
        } else {
            handleDocumentFile(file);
        }
    });
}

function handleAudioFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الصوت: ${file.name}. يمكن تحليله لاستخراج النص.`);
}

function handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageHtml = `
            <p>تم تحميل الصورة: ${file.name}</p>
            <img src="${e.target.result}" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
        `;
        addMessageToChat('assistant', imageHtml, 'html');
    };
    reader.readAsDataURL(file);
}

function handleDocumentFile(file) {
    addMessageToChat('assistant', `تم تحميل المستند: ${file.name}. يمكن قراءته وتحليله.`);
}

function playWelcomeSound() {
    // محاولة تشغيل صوت الترحيب
    const audio = new Audio('voice/iraqi_greeting.mp3');
    audio.volume = assistantSettings.speechVolume;
    audio.play().catch(e => {
        console.log('لا يمكن تشغيل صوت الترحيب:', e);
        // بديل نصي
        setTimeout(() => {
            speakText('يا قائم آل محمد، أهلاً وسهلاً بك في المساعد التقني الذكي');
        }, 1000);
    });
}

function loadSettings() {
    const saved = localStorage.getItem('assistantSettings');
    if (saved) {
        try {
            const savedSettings = JSON.parse(saved);
            assistantSettings = { ...assistantSettings, ...savedSettings };
        } catch (e) {
            console.error('خطأ في تحميل الإعدادات:', e);
        }
    }
}

function saveSettings() {
    try {
        localStorage.setItem('assistantSettings', JSON.stringify(assistantSettings));
    } catch (e) {
        console.error('خطأ في حفظ الإعدادات:', e);
    }
}

function closeDisplayArea() {
    const displayArea = document.getElementById('displayArea');
    if (displayArea) {
        displayArea.style.display = 'none';
    }
}

function closeSettingsModal() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// تصدير الوظائف للاستخدام العام
window.sendMessage = sendMessage;
window.startScreenShare = startScreenShare;
window.uploadVideo = uploadVideo;
window.analyzeVideo = analyzeVideo;
window.show3DView = show3DView;
window.generateSummary = generateSummary;
window.toggleVoiceSettings = toggleVoiceSettings;
window.toggleVoiceRecording = toggleVoiceRecording;
window.speakText = speakText;
window.initializeAssistant = initializeAssistant;
window.playWelcomeSound = playWelcomeSound;



// معالجة ملفات الصوت
function handleAudioFile(file) {
    addMessageToChat('assistant', `تم تحميل ملف الصوت: ${file.name}. جاري المعالجة...`);
}

// معالجة الصور
function handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const imageHtml = `
            <p>تم تحميل الصورة: ${file.name}</p>
            <img src="${e.target.result}" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
        `;
        addMessageToChat('assistant', imageHtml, 'html');
    };
    reader.readAsDataURL(file);
}

// معالجة المستندات
function handleDocumentFile(file) {
    addMessageToChat('assistant', `تم تحميل المستند: ${file.name}. جاري القراءة والتحليل...`);
}

// حفظ وتحميل الإعدادات
function loadSettings() {
    const saved = localStorage.getItem('assistantSettings');
    if (saved) {
        assistantSettings = { ...assistantSettings, ...JSON.parse(saved) };
    }
}

function saveSettings() {
    localStorage.setItem('assistantSettings', JSON.stringify(assistantSettings));
}

// إغلاق منطقة العرض
function closeDisplayArea() {
    document.getElementById('displayArea').style.display = 'none';
}

// إغلاق نافذة الإعدادات
function closeSettingsModal() {
    document.getElementById('settingsModal').style.display = 'none';
}
