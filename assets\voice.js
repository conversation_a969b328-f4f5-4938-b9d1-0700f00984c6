// ملف التفاعل الصوتي والدعم للهجة العراقية
// Voice Interaction and Iraqi Dialect Support

class VoiceAssistant {
    constructor() {
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.isListening = false;
        this.iraqi_phrases = this.initializeIraqiPhrases();
        this.voice_commands = this.initializeVoiceCommands();
    }

    // تهيئة العبارات العراقية
    initializeIraqiPhrases() {
        return {
            // تحيات
            'شلونك': 'كيف حالك',
            'شلونكم': 'كيف حالكم',
            'أهلين': 'أهلاً وسهلاً',
            'وين راح': 'أين ذهب',
            'شنو هذا': 'ما هذا',
            'شگد': 'كم',
            'وين': 'أين',
            'شنو': 'ماذا',
            'ليش': 'لماذا',
            'متى': 'متى',
            'كيف': 'كيف',
            
            // تعبيرات تقنية
            'شلون أسوي': 'كيف أعمل',
            'وين أحصل': 'أين أجد',
            'شنو يعني': 'ما معنى',
            'كيف أقدر': 'كيف يمكنني',
            'شلون أخلي': 'كيف أجعل',
            'وين أروح': 'أين أذهب',
            
            // أوامر
            'فتح': 'افتح',
            'سكر': 'أغلق',
            'شغل': 'تشغيل',
            'طفي': 'إطفاء',
            'حفظ': 'احفظ',
            'امسح': 'احذف',
            
            // استجابات
            'زين': 'جيد',
            'ماشي': 'موافق',
            'تمام': 'تمام',
            'خلاص': 'انتهى',
            'يلا': 'هيا',
            'بس': 'فقط'
        };
    }

    // تهيئة الأوامر الصوتية
    initializeVoiceCommands() {
        return {
            'فتح مشاركة الشاشة': () => startScreenShare(),
            'تحميل فيديو': () => uploadVideo(),
            'تحليل فيديو': () => analyzeVideo(),
            'عرض ثلاثي الأبعاد': () => show3DView(),
            'توليد ملخص': () => generateSummary(),
            'إعدادات الصوت': () => toggleVoiceSettings(),
            'مسح المحادثة': () => this.clearChat(),
            'حفظ المحادثة': () => this.saveConversation(),
            'قراءة آخر رسالة': () => this.readLastMessage()
        };
    }

    // تحويل النص العراقي إلى عربي فصيح
    convertIraqiToArabic(text) {
        let convertedText = text;
        
        // تحويل العبارات العراقية
        for (const [iraqi, arabic] of Object.entries(this.iraqi_phrases)) {
            const regex = new RegExp(iraqi, 'gi');
            convertedText = convertedText.replace(regex, arabic);
        }
        
        return convertedText;
    }

    // بدء التعرف على الكلام
    startListening() {
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            this.showError('المتصفح لا يدعم التعرف على الكلام');
            return;
        }

        this.recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
        this.setupRecognition();
        
        try {
            this.recognition.start();
            this.isListening = true;
            console.log('🎤 بدء الاستماع للأوامر الصوتية');
        } catch (error) {
            this.showError('خطأ في بدء التعرف على الكلام: ' + error.message);
        }
    }

    // إعداد التعرف على الكلام
    setupRecognition() {
        this.recognition.lang = 'ar-SA'; // العربية السعودية كأساس
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.maxAlternatives = 3;

        this.recognition.onstart = () => {
            console.log('🎤 بدء التسجيل الصوتي');
            this.updateVoiceButton(true);
        };

        this.recognition.onresult = (event) => {
            const results = event.results[0];
            let transcript = results[0].transcript;
            
            console.log('🗣️ النص المسجل:', transcript);
            console.log('🎯 مستوى الثقة:', results[0].confidence);
            
            // تحويل اللهجة العراقية
            const convertedText = this.convertIraqiToArabic(transcript);
            console.log('🔄 النص المحول:', convertedText);
            
            // معالجة الأوامر الصوتية
            this.processVoiceCommand(convertedText, transcript);
        };

        this.recognition.onerror = (event) => {
            console.error('❌ خطأ في التعرف على الكلام:', event.error);
            this.handleRecognitionError(event.error);
        };

        this.recognition.onend = () => {
            console.log('⏹️ انتهاء التسجيل الصوتي');
            this.isListening = false;
            this.updateVoiceButton(false);
        };
    }

    // معالجة الأوامر الصوتية
    processVoiceCommand(convertedText, originalText) {
        // البحث عن أوامر مباشرة
        for (const [command, action] of Object.entries(this.voice_commands)) {
            if (convertedText.includes(command) || originalText.includes(command)) {
                console.log('🎯 تنفيذ الأمر:', command);
                action();
                this.speakResponse(`تم تنفيذ الأمر: ${command}`);
                return;
            }
        }

        // إذا لم يكن أمراً مباشراً، أرسله كرسالة عادية
        document.getElementById('messageInput').value = convertedText;
        
        // إرسال الرسالة تلقائياً إذا كانت تحتوي على سؤال
        if (this.isQuestion(convertedText)) {
            sendMessage();
        }
    }

    // التحقق من كون النص سؤالاً
    isQuestion(text) {
        const questionWords = ['كيف', 'ماذا', 'أين', 'متى', 'لماذا', 'من', 'شلون', 'شنو', 'وين', 'ليش'];
        return questionWords.some(word => text.includes(word)) || text.includes('؟');
    }

    // قراءة النص بصوت عالي
    speakResponse(text, options = {}) {
        if (!this.synthesis) {
            console.warn('⚠️ المتصفح لا يدعم تحويل النص إلى كلام');
            return;
        }

        // إيقاف أي كلام سابق
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        
        // إعدادات الصوت
        utterance.lang = options.lang || assistantSettings.language || 'ar-SA';
        utterance.rate = options.rate || assistantSettings.speechRate || 1;
        utterance.volume = options.volume || assistantSettings.speechVolume || 0.8;
        utterance.pitch = options.pitch || 1;

        // اختيار صوت عربي إذا كان متاحاً
        const voices = this.synthesis.getVoices();
        const arabicVoice = voices.find(voice => 
            voice.lang.startsWith('ar') || 
            voice.name.includes('Arabic') ||
            voice.name.includes('عربي')
        );
        
        if (arabicVoice) {
            utterance.voice = arabicVoice;
            console.log('🔊 استخدام الصوت العربي:', arabicVoice.name);
        }

        // أحداث الكلام
        utterance.onstart = () => {
            console.log('🔊 بدء قراءة النص');
        };

        utterance.onend = () => {
            console.log('✅ انتهاء قراءة النص');
        };

        utterance.onerror = (event) => {
            console.error('❌ خطأ في قراءة النص:', event.error);
        };

        this.synthesis.speak(utterance);
    }

    // تحديث زر الصوت
    updateVoiceButton(isRecording) {
        const voiceBtn = document.getElementById('voiceRecordBtn');
        if (voiceBtn) {
            if (isRecording) {
                voiceBtn.classList.add('recording');
                voiceBtn.title = 'جاري التسجيل... انقر للتوقف';
            } else {
                voiceBtn.classList.remove('recording');
                voiceBtn.title = 'اضغط للتحدث';
            }
        }
    }

    // معالجة أخطاء التعرف على الكلام
    handleRecognitionError(error) {
        let errorMessage = 'حدث خطأ في التعرف على الكلام';
        
        switch (error) {
            case 'no-speech':
                errorMessage = 'لم يتم اكتشاف أي كلام. يرجى المحاولة مرة أخرى';
                break;
            case 'audio-capture':
                errorMessage = 'لا يمكن الوصول إلى الميكروفون';
                break;
            case 'not-allowed':
                errorMessage = 'تم رفض الإذن للوصول إلى الميكروفون';
                break;
            case 'network':
                errorMessage = 'خطأ في الشبكة';
                break;
        }
        
        this.showError(errorMessage);
        this.isListening = false;
        this.updateVoiceButton(false);
    }

    // عرض رسالة خطأ
    showError(message) {
        console.error('❌', message);
        addMessageToChat('assistant', `خطأ: ${message}`);
    }

    // إيقاف التسجيل
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    // مسح المحادثة
    clearChat() {
        const chatContainer = document.getElementById('chatContainer');
        chatContainer.innerHTML = '';
        currentConversation = [];
        this.speakResponse('تم مسح المحادثة');
    }

    // حفظ المحادثة
    saveConversation() {
        const conversation = JSON.stringify(currentConversation, null, 2);
        const blob = new Blob([conversation], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `محادثة_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.speakResponse('تم حفظ المحادثة');
    }

    // قراءة آخر رسالة
    readLastMessage() {
        if (currentConversation.length > 0) {
            const lastMessage = currentConversation[currentConversation.length - 1];
            this.speakResponse(lastMessage.content);
        } else {
            this.speakResponse('لا توجد رسائل للقراءة');
        }
    }
}

// إنشاء مثيل المساعد الصوتي
const voiceAssistant = new VoiceAssistant();

// ربط الأحداث الصوتية
document.addEventListener('DOMContentLoaded', function() {
    // تحديث زر التسجيل الصوتي
    const voiceRecordBtn = document.getElementById('voiceRecordBtn');
    if (voiceRecordBtn) {
        voiceRecordBtn.addEventListener('click', function() {
            if (voiceAssistant.isListening) {
                voiceAssistant.stopListening();
            } else {
                voiceAssistant.startListening();
            }
        });
    }
    
    // تحميل الأصوات المتاحة
    if (window.speechSynthesis) {
        window.speechSynthesis.onvoiceschanged = function() {
            const voices = window.speechSynthesis.getVoices();
            console.log('🔊 الأصوات المتاحة:', voices.map(v => `${v.name} (${v.lang})`));
        };
    }
});

// تصدير المساعد الصوتي للاستخدام العام
window.voiceAssistant = voiceAssistant;
